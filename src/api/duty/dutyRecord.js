import request from '@/utils/request'

// 查看表单信息
export function list(params) {
  return request({
    url: `/d15DutyRecord/queryList`,
    method: 'get',
    params
  })
}

export function save(params) {
  return request({
    url: '/d15DutyRecord/save',
    method: 'post',
    data: params
  })
}

export function read(params) {
  return request({
    url: '/d15DutyRecord/read',
    method: 'post',
    data: params
  })
}

export function queryById(params) {
  return request({
    url: '/d15DutyRecord/queryById',
    method: 'get',
    params
  })
}

export function batchDel(params) {
  return request({
    url: '/d15DutyRecord/batchDel',
    method: 'post',
    data: params
  })
}

export function mark(params) {
  return request({
    url: '/d15DutyRecord/mark',
    method: 'post',
    data: params
  })
}

export function getNumber(params) {
  return request({
    url: '/d15DutyRecord/getMaxNumber',
    method: 'get',
    params
  })
}

export function getDepts() {
  return request({
    url: '/api/dept/queryAllNew',
    method: 'get'
  })
}

export function getUnitUsersTree(params) {
  return request({
    url: '/api/users/getUnitUsersTree',
    method: 'get',
    params
  })
}

export function getFeedbackList(number) {
  return request({
    url: '/d15DutyRecord/getFeedbackList?number=' + number,
    method: 'get'
  })
}

export default { list, save, read, queryById, batchDel, getNumber, getDepts, getUnitUsersTree, mark, getFeedbackList }
