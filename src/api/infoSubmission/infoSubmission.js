import request from '@/utils/request'
import qs from 'qs'
export function queryPageInfo(params) { // 分页查新信息报送数据
  return request({
    url: `api/aigov/zjedu/infoManager/queryPageInfo?${qs.stringify(params, { indices: false })}`,
    method: 'get'
  })
}

export function queryPageInfoSearch(params) { // 分页查新信息报送数据
  return request({
    url: `api/aigov/zjedu/infoManager/queryPageInfoSearch?${qs.stringify(params, { indices: false })}`,
    method: 'get'
  })
}

export function updateInfoStatusById(params) { // 根据id修改信息状态
  return request({
    url: 'api/aigov/zjedu/infoManager/updateInfoStatusById',
    method: 'post',
    params
  })
}

export function doAddInfo(params) { // 新增信息报送
  return request({
    url: 'api/aigov/zjedu/infoManager/addInfo',
    method: 'post',
    data: params
  })
}

export function doDeleteInfo(params) { // 根据id删除信息报送
  return request({
    url: 'api/aigov/zjedu/infoManager/deleteInfo',
    method: 'post',
    data: params
  })
}

export function doUpdateInfo(params) { // 修改信息报送
  return request({
    url: 'api/aigov/zjedu/infoManager/updateInfo',
    method: 'post',
    data: params
  })
}

export function queryInfoById(params) { // 根据主键搜索信息报送
  return request({
    url: 'api/aigov/zjedu/infoManager/queryInfoById',
    method: 'get',
    params: params
  })
}

export function queryInfoTypeInDict(param) { // 搜索字典中配置的信息类型
  return request({
    url: '/api/dictDetail/map',
    method: 'get',
    params: param
  })
}

export function queryFileByIds(ids) { // 搜索上传的附件
  return request({
    url: '/api/localStorage/findByIds',
    method: 'post',
    params: ids
  })
}

export function downloadAttach(id) { // 下载附件
  return request({
    url: '/api/localStorage/downloadFile/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

export function deleteAttach(ids) { // 删除附件
  return request({
    url: '/api/localStorage/',
    method: 'delete',
    data: ids
  })
}

export function queryInfoUseLogPage(params) { // 分页搜索信息录用记录
  return request({
    url: '/api/aigov/zjedu/infoUseLog/queryPage?' + qs.stringify(params),
    method: 'get',
    params
  })
}

export function addInfoUseLog(params) { // 新增信息录用记录
  return request({
    url: '/api/aigov/zjedu/infoUseLog/addInfoUseLog',
    method: 'post',
    data: params
  })
}

export function updateInfoUseLog(params) { // 修改信息录用记录
  return request({
    url: '/api/aigov/zjedu/infoUseLog/updateInfoUseLog',
    method: 'post',
    data: params
  })
}

export function deleteInfoUseLog(params) { // 删除信息录用记录
  return request({
    url: '/api/aigov/zjedu/infoUseLog/deleteInfoUseLog',
    method: 'post',
    params
  })
}

export function queryUseDate(params) { // 搜索:信息计分统计-录用记录
  return request({
    url: '/api/aigov/zjedu/infoUseLog/queryUseDate?' + qs.stringify(params),
    method: 'get'
  })
}

export function submitStatistics(params) { // 搜索:信息计分统计-上报统计
  return request({
    url: '/api/aigov/zjedu/infoUseLog/submitStatistics?' + qs.stringify(params),
    method: 'get',
    data: params
  })
}

export function getInfoUseLogDetail(params) { // 搜索:信息计分统计-录用详细情况
  return request({
    url: '/api/aigov/zjedu/infoUseLog/getInfoUseLogDetail?' + qs.stringify(params),
    method: 'get',
    data: params
  })
}

export default {
  queryPageInfo,
  updateInfoStatusById,
  doAddInfo,
  doDeleteInfo,
  doUpdateInfo,
  queryInfoTypeInDict,
  queryInfoById,
  queryFileByIds,
  downloadAttach,
  queryInfoUseLogPage,
  addInfoUseLog,
  queryUseDate,
  submitStatistics,
  getInfoUseLogDetail,
  deleteAttach,
  updateInfoUseLog,
  deleteInfoUseLog,
  queryPageInfoSearch
}
