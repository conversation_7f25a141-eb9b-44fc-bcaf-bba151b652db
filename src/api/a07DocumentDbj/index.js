import request from '@/utils/request'

// 督办件列表不分页（流程办理页使用）
export function list(params) {
  return request({
    url: '/api/a07DocumentDbj/list',
    method: 'get',
    params: params
  })
}

// 督办件列表分页
export function page(params) {
  return request({
    url: '/api/a07DocumentDbj/page',
    method: 'get',
    params: params
  })
}

// 新建督办
export function create(data) {
  return request({
    url: '/api/a07DocumentDbj/create',
    method: 'post',
    data
  })
}

// 从列表创建督查督办
export function createFromOther(data) {
  return request({
    url: '/api/a07DocumentDbj/createFromOther',
    method: 'post',
    params: data
  })
}

export function del(data) {
  return request({
    url: '/api/a07DocumentDbj/delete',
    method: 'delete',
    params: data
  })
}
// 编号生成
export function bhsc(params) {
  return request({
    url: '/api/a07DocumentDbj/bh',
    method: 'get',
    params: params
  })
}

// 获取督办单的相关收文或领导批示
export function sw(params) {
  return request({
    url: '/api/a07DocumentDbj/sw',
    method: 'get',
    params: params
  })
}

// 收文、领导批示
export function swList(params) {
  return request({
    url: '/api/a07DocumentDbj/swList',
    method: 'get',
    params: params
  })
}

export function detail(params) {
  return request({
    url: '/api/a07DocumentDbj/detail',
    method: 'get',
    params: params
  })
}

export default { list, page, create, createFromOther, del, bhsc, sw, swList }
