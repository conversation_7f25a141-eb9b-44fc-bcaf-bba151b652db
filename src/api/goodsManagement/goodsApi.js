import request from '@/utils/request'

// -----------------------------物品管理相关请求-----------------------------------------
// 分页搜索物品
export function goodsQueryPage(data) {
  return request({
    url: '/api/goodsmanagement/goods/queryPage',
    method: 'get',
    params: data
  })
}

// 添加物品
export function addGoods(data) {
  return request({
    url: '/api/goodsmanagement/goods/add',
    method: 'post',
    data: data
  })
}

// 删除物品
export function deleteGoodsById(data) {
  return request({
    url: '/api/goodsmanagement/goods/deleteById',
    method: 'post',
    params: data
  })
}

// 预警物品搜索
export function queryPageWarning(data) {
  return request({
    url: '/api/goodsmanagement/goods/queryPageWarning',
    method: 'get',
    params: data
  })
}

// 修改物品启用、禁用状态
export function updateGoods(data) {
  return request({
    url: '/api/goodsmanagement/goods/update',
    method: 'post',
    data: data
  })
}
// ------------------------------物品分类相关请求---------------------------------------
// 搜索物品分类树
export function queryTreeData(data) {
  return request({
    url: '/api/goodsmanagement/category/queryTreeData',
    method: 'get',
    params: data
  })
}

// 一次性搜索树结构（所有数据）
export function queryAllTreeData(data) {
  return request({
    url: '/api/goodsmanagement/category/queryAllTreeData',
    method: 'get',
    params: data
  })
}

// 搜索所有物品分类
export function queryCategoryAll(data) {
  return request({
    url: '/api/goodsmanagement/category/queryAll',
    method: 'get',
    params: data
  })
}

// 添加/修改分类
export function addGoodsCategory(data) {
  return request({
    url: '/api/goodsmanagement/category/add',
    method: 'post',
    data: data
  })
}

// 删除分类
export function deleteGoodsCategory(data) {
  return request({
    url: '/api/goodsmanagement/category/delete',
    method: 'post',
    params: data
  })
}

// ----------------------------------------------------物品入库---------------------------------------------
// 分页搜索物品入库信息
export function goodsStoreQueryPage(data) {
  return request({
    url: '/api/goodsmanagement/store/queryPageByGoodId',
    method: 'get',
    params: data
  })
}

// 添加物品入库
export function addGoodsStore(data) {
  return request({
    url: '/api/goodsmanagement/store/add',
    method: 'post',
    data: data
  })
}

// ----------------------------------------------------------物品领用-----------------------------------------------------------

// 物品领用记录(根据物品id搜索)
export function goodsSuppliesRecord(data) {
  return request({
    url: '/api/goodsmanagement/Supplies/queryPageGoodsByGoodsId',
    method: 'get',
    params: data
  })
}

// 物品领用流程待办、已办
export function goodsSuppliesQueryPage(data) {
  return request({
    url: '/api/goodsmanagement/Supplies/queryPageGoods',
    method: 'get',
    params: data
  })
}

export default {
  // 物品
  goodsQueryPage, addGoods, deleteGoodsById, queryPageWarning, updateGoods,

  // 物品分类
  queryCategoryAll, queryTreeData, addGoodsCategory, deleteGoodsCategory, queryAllTreeData,

  // 物品入库
  goodsStoreQueryPage, addGoodsStore,

  // 物品领用
  goodsSuppliesQueryPage, goodsSuppliesRecord
}
