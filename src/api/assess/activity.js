import request from '@/utils/request'
import qs from 'qs'

export function list(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/list',
    method: 'get',
    params
  })
}

export function activity(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/activity',
    method: 'get',
    params
  })
}

export function save(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/save',
    method: 'post',
    data: params
  })
}

export function batchDel(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/batchDel',
    method: 'delete',
    data: params
  })
}

export function deptTree(params) {
  return request({
    url: '/assess/deptTree',
    method: 'get',
    params
  })
}

export function dept(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/dept',
    method: 'get',
    params
  })
}

export function saveDept(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/saveDept',
    method: 'post',
    data: params
  })
}

export function deptDel(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/dept/batchDel',
    method: 'delete',
    data: params
  })
}

export function users(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/users',
    method: 'get',
    params
  })
}

export function saveUser(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/saveUser',
    method: 'post',
    data: params
  })
}

export function syncUsers(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/syncUsers',
    method: 'post',
    data: params
  })
}

export function getByYearAndQuarter(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/getByYearAndQuarter',
    method: 'get',
    params
  })
}

export function leadership(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/leadership',
    method: 'get',
    params
  })
}

/**
 * 查找正在进行中和已结束活动
 * @param params
 * @returns {AxiosPromise}
 */
export function findActivities(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/findActivities',
    method: 'get',
    params
  })
}

export function exportAssessUser(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/assess/exportAssessUser' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}

export default { list, activity, save, batchDel, deptTree, dept, saveDept, deptDel, users, saveUser, syncUsers, getByYearAndQuarter, leadership, findActivities, exportAssessUser }
