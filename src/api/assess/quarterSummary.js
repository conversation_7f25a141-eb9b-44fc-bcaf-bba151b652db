import request from '@/utils/request'

export function list(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/list',
    method: 'get',
    params
  })
}

export function detail(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/detail',
    method: 'get',
    params
  })
}

export function save(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/save',
    method: 'post',
    data: params
  })
}

export function batchDel(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/batchDel',
    method: 'delete',
    data: params
  })
}

export function saveList(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/saveList',
    method: 'post',
    data: params
  })
}

export function findById(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/findById',
    method: 'get',
    params
  })
}
export default { list, detail, save, batchDel, saveList, findById }
