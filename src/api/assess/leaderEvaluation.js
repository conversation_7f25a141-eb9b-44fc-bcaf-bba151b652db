import request from '@/utils/request'
import qs from 'qs'

export function list(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/leaderEvaluation/list',
    method: 'get',
    params
  })
}

export function save(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/leaderEvaluation/save',
    method: 'post',
    data: params
  })
}

export function exportExcel(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/leaderEvaluation/exportExcel' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}
export function getUnsubmittedUsers(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/quarterlySummary/getUnsubmittedUsers',
    method: 'get',
    params
  })
}
export default { list, save, exportExcel }
