import request from '@/utils/request'
import qs from 'qs'

export function list(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/list',
    method: 'get',
    params
  })
}

export function detail(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/detail',
    method: 'get',
    params
  })
}

export function save(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/save',
    method: 'post',
    data: params
  })
}

export function batchDel(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/batchDel',
    method: 'delete',
    data: params
  })
}

export function curHeadOrLeader(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/curHeadOrLeader',
    method: 'get',
    params
  })
}

export function pdfView(params) {
  return request({
    url: process.env.VUE_APP_BASE_API + '/monthlyReport/pdfView' + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}
export default { list, detail, save, batchDel, curHeadOrLeader, pdfView }
