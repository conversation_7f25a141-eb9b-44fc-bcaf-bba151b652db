import request from '@/utils/request'

// 任务列表
export function educationBar(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/jysyfztj',
    method: 'get',
    params: data
  })
}
export function reformDynamics(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/szgg',
    method: 'get',
    params: data
  })
}
export function highLightWork(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/xzzj',
    method: 'get',
    params: data
  })
}
// 获取筛选条件
export function jysySearch(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/jysySearch',
    method: 'get',
    params: data
  })
}
// 核心数据业务
export function coreBusiness(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/hxyw',
    method: 'get',
    params: data
  })
}
export function educationData(data) {
  return request({
    url: process.env.VUE_APP_MOCK_API + '/51/api/jytzzmh/jydsj',
    method: 'get',
    params: data
  })
}
