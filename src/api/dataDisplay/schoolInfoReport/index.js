import request from '@/utils/request'

// todo
/**
 * @method 获取教育常用数据
 * @params
 */
function getSchoolReport(params) {
  return request({
    url: '/api/school',
    method: 'get',
    params
  })
}

/**
 * @method 获取教育常用数据
 * @params
 */
function getSchoolTypeGroup() {
  return request({
    url: '/api/school/schoolTypeGroup',
    method: 'get'
  })
}
export default { getSchoolReport, getSchoolTypeGroup }
