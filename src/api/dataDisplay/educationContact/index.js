import request from '@/utils/request'

// todo
/**
 * @method 发送短信(内容)
 * @params
 */
function sendSMContent(data) {
  return request({
    url: '/api/mas/sendSMContent',
    method: 'POST',
    data
  })
}

function officeDirectoryList(params) {
  return request({
    url: '/api/aigov/phoneBook/officeDirectoryList',
    method: 'GET',
    params
  })
}
function createOrUpdate(params) {
  return request({
    url: '/api/aigov/phoneBook/createOrUpdate',
    method: 'POST',
    data: params
  })
}
function details(params) {
  return request({
    url: '/api/aigov/phoneBook/details',
    method: 'GET',
    params
  })
}

export default { sendSMContent, officeDirectoryList, createOrUpdate, details }
