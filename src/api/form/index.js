import request from '@/utils/request'

// 获取填报表单接口
export function form(id) {
  return request({
    url: '/api/form/' + id,
    method: 'get'

  })
}

export function submitForm(data) {
  return request({
    url: '/api/form/submitForm',
    method: 'post',
    data
  })
}
export function getFormData(data) {
  return request({
    url: '/api/form/getFormData',
    method: 'get',
    params: data
  })
}
