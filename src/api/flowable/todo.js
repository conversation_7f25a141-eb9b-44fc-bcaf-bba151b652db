import request from '@/utils/request'

// 搜索待办任务列表
export function todoList(query) {
  return request({
    url: '/api/flowable/task/queryTodoList',
    method: 'post',
    data: query
  })
}

// 完成任务
export function complete(data) {
  return request({
    url: '/api/flowable/task/complete',
    method: 'post',
    data: data
  })
}

// 退回任务
export function returnTask(data) {
  return request({
    url: '/api/flowable/task/return',
    method: 'post',
    data: data
  })
}

// 驳回任务
export function rejectTask(data) {
  return request({
    url: '/api/flowable/task/reject',
    method: 'post',
    data: data
  })
}

// 可退回任务列表
export function returnList(data) {
  return request({
    url: '/api/flowable/task/returnList',
    method: 'post',
    data: data
  })
}

// 读取image文件
export function diagram(procInsId) {
  return request({
    url: '/api/flowable/task/diagram/' + procInsId,
    method: 'get',
    responseType: 'blob'
  })
}
// 流程跟踪
export function diagramJSON(processInstanceId, processDefinitionId) {
  return request({
    url: `/api/bpm/instance/diagram/model-json/${processInstanceId}/${processDefinitionId}`,
    method: 'get'
  })
}
// 锁定任务
export function taskLock(data) {
  return request({
    url: '/api/flowable/task/taskLock',
    method: 'post',
    data: data
  })
}

// 解锁任务
export function taskUnLock(data) {
  return request({
    url: '/api/flowable/task/taskUnLock',
    method: 'post',
    data: data
  })
}
