import request from '@/utils/request'

// 流程搜索
export function queryProcessList(query) {
  return request({
    url: '/api/bpm/instance/queryProcess',
    method: 'post',
    data: query
  })
}

// 完成任务
export function complete(data) {
  return request({
    url: '/api/flowable/task/complete',
    method: 'post',
    data: data
  })
}

// 撤回任务
export function revokeProcess(data) {
  return request({
    url: '/api/flowable/task/revokeProcess',
    method: 'post',
    data: data
  })
}

// 撤销任务
export function stopProcess(data) {
  return request({
    url: '/api/flowable/task/stopProcess',
    method: 'post',
    data: data
  })
}

// 驳回任务
export function rejectTask(data) {
  return request({
    url: '/api/flowable/task/reject',
    method: 'post',
    data: data
  })
}

// 可退回任务列表
export function returnList(data) {
  return request({
    url: '/api/flowable/task/returnList',
    method: 'post',
    data: data
  })
}

// 搜索流程相关id
export function queryParamsByProcInsId(query) {
  return request({
    url: '/api/bpm/instance/queryParamsByProcInsId',
    method: 'get',
    params: query
  })
}

// 搜索流程相关信息
export function getProcessInfoByProcInsId(query) {
  return request({
    url: '/api/bpm/instance/getProcessInfoByProcInsId',
    method: 'get',
    params: query
  })
}
// 搜索任务相关信息
export function getTaskInfoByProcInsId(query) {
  return request({
    url: '/api/bpm/instance/getTaskInfoByProcInsId',
    method: 'get',
    params: query
  })
}
// 获取流程类别
export function getWorkflowTypes() {
  return request({
    url: '/api/bpm/document/getWorkflowTypes',
    method: 'get'
  })
}
