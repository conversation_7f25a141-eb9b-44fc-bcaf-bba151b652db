import request from '@/utils/request'

// 搜索已办任务列表
export function finishedList(query) {
  return request({
    url: '/api/flowable/task/finishedList',
    method: 'get',
    params: query
  })
}

// 任务流转记录
export function flowRecord(query) {
  return request({
    url: '/api/bpm/instance/flowRecord',
    method: 'get',
    params: query
  })
}

// 删除流程实例
export function delProcInstance(data) {
  return request({
    url: '/api/bpm/instance/delete',
    method: 'delete',
    params: data
  })
}

// 搜索加减签的列表
export function getTaskAssignees(Id) {
  return request({
    url: '/api/bpm/instance/getTaskAssignees/' + Id,
    method: 'get'
  })
}

// 加减签提交
export function signTask(data) {
  return request({
    url: '/api/flowable/task/signTask',
    method: 'POST',
    data
  })
}
