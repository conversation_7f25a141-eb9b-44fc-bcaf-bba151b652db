import request from '@/utils/request'

// 搜索流程表单列表
export function listForm(query) {
  return request({
    url: '/api/form/queryAll',
    method: 'get',
    params: query
  })
}

// 搜索流程表单详细
export function getForm(formId) {
  return request({
    url: '/api/form/' + formId,
    method: 'get'
  })
}

// 新增流程表单
export function addForm(data) {
  return request({
    url: '/api/flowable/form',
    method: 'post',
    data: data
  })
}

// 修改流程表单
export function updateForm(data) {
  return request({
    url: '/api/flowable/form',
    method: 'put',
    data: data
  })
}
// 修改流程表单
export function updateFormField(data) {
  return request({
    url: '/api/form/updateFormField',
    method: 'post',
    data: data
  })
}

// 挂载表单
export function addDeployForm(data) {
  return request({
    url: '/api/form/addDeployForm',
    method: 'post',
    data: data
  })
}

// 删除流程表单
export function delForm(formId) {
  return request({
    url: '/api/flowable/form/' + formId,
    method: 'delete'
  })
}

// 导出流程表单
export function exportForm(query) {
  return request({
    url: '/api/flowable/form/export',
    method: 'get',
    params: query
  })
}

export function updateDeployForm(params) {
  return request({
    url: '/api/form/updateDeployForm',
    method: 'post',
    data: params
  })
}

// 根据流程实例id获取表单历史版本数据
export function queryHistoricalFormData(query) {
  return request({
    url: '/api/historical/formdata/queryHistoricalFormData',
    method: 'get',
    params: query
  })
}

// 根据流程实例id获取可编辑表单数据
export function getWritableFormData(query) {
  return request({
    url: '/api/form/getWritableFormData',
    method: 'get',
    params: query
  })
}

// 更新表单数据
export function updateFormData(data) {
  return request({
    url: '/api/form/updateFormData',
    method: 'POST',
    data
  })
}
