import request from '@/utils/request'

/**
 * 获取目录列表树
 * @param {id} data
 * @returns
 */
export function getListTree() {
  return request({
    url: '/api/fileClassify/getListTree',
    method: 'post'
  })
}

/**
 * 搜索文件列表
 * @param {id} data
 * @returns
 */
export function getFileList(data) {
  return request({
    url: '/api/file/getList',
    method: 'post',
    data
  })
}
/**
 * 新增或修改文件目录
 * @param {id} data
 * @returns
 */
export function addOrUpdate(data) {
  return request({
    url: '/api/fileClassify/addOrUpdate',
    method: 'post',
    data
  })
}
/**
 * 上传文件
 * @param {classifyId} query
 *
 * @param {sort} query
 * @param {id} 用作修改上传文件query
 * @returns
 */
export function uploadFile(data) {
  return request({
    url: '/api/file/uploadFile',
    method: 'post',
    data
  })
}

/**
 * 更新文件信息(仅修改了文件名,排序)
 * @param {*id} data
 * @param {*name} data
 * @param {*sort} data
 * @returns
 */
export function updateFileInfo(data) {
  return request({
    url: '/api/file/updateFileInfo',
    method: 'post',
    data
  })
}

/**
 * 删除文件
 * @param {ids} array
 * @param
 * @returns
 */
export function deleteFiles(data) {
  return request({
    url: '/api/file/delete',
    method: 'post',
    data
  })
}

/**
 * 删除目录
 * @param {id} int
 * @param
 * @returns
 */
export function deleteContent(data) {
  return request({
    url: '/api/fileClassify/delete',
    method: 'post',
    data
  })
}
/**
 *
 * @param {*} data
 * @param {*} data
 * @param {*} data
 * @returns
 */
