import request from '@/utils/request'

export function fileConversionInfo(data) {
  return request({
    url: '/api/docconvert/fileConversionInfo',
    method: 'POST',
    params: data
  })
}
// 一个获取关联文件意见的
export function relationFile(origParams) {
  // 添加相关文件精度参数 如需调整精度可在此处调整
  const params = { tpercent: '7', zpercent: '7', ...origParams }
  return request({
    url: '/api/search/relationFile',
    method: 'get',
    params
  })
}
// 获取跳转需要用到的procInsId
export function relationFlow(origParams) {
  // 添加相关文件精度参数 如需调整精度可在此处调整
  const params = { tpercent: '7', zpercent: '7', ...origParams }
  return request({
    url: '/api/search/relationFlow',
    method: 'get',
    params
  })
}
export function convertFormat(data) {
  return request({
    url: '/api/docconvert/convertFormat',
    method: 'POST',
    data
  })
}
