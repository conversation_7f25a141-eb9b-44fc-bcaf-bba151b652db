import request from '@/utils/request'

export function doRemindAllFilled(data) {
  return request({
    url: '/api/aigov/zjedu/infoSchoolSafe/doRemindAllFilled',
    method: 'post',
    params: data
  })
}
export function queryUnfilledOrg(queryParams) {
  return request({
    url: '/api/aigov/zjedu/infoSchoolSafe/queryUnfilledOrg',
    method: 'get',
    params: queryParams
  })
}

export function exportUnfilledOrg(data) {
  return request({
    url: '/api/aigov/zjedu/infoSchoolSafe/exportUnfilledOrg',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}
