import request from '@/utils/request'

/**
 * 分页搜索-所有已签收协同
 * @param {*} queryParams
 * @returns
 */
export function signedList(queryParams) {
  return request({
    url: 'api/aigov/collaboration/info/signedList',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索-所有待签收协同
 * @param {*} queryParams
 * @returns
 */
export function unsignList(queryParams) {
  return request({
    url: 'api/aigov/collaboration/info/unsignList',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索-所有工作协同
 * @param {*} queryParams
 * @returns
 */
export function allList(queryParams) {
  return request({
    url: 'api/aigov/collaboration/info/allList',
    method: 'get',
    params: queryParams
  })
}

/**
 * 工作协同详情-编辑
 * @param {*} queryParams
 * @returns
 */
export function editDetail(queryParams) {
  return request({
    url: 'api/aigov/collaboration/info/editDetail',
    method: 'get',
    params: queryParams
  })
}

/**
 * 工作协同详情-查看
 * @param {number} id
 * @returns
 */
export function view(id) {
  return request({
    url: 'api/aigov/collaboration/info/view',
    method: 'get',
    params: { id: id }
  })
}

/**
 * 工作协同-保存或发送
 * @param {*} data
 * @returns
 */
export function save(data) {
  return request({
    url: 'api/aigov/collaboration/info/save',
    method: 'post',
    data
  })
}

/**
 * 工作协同-首读
 * @param {number} id
 * @returns
 */
export function readFirst(id) {
  return request({
    url: 'api/aigov/collaboration/info/readFirst',
    method: 'post',
    params: { id: id }
  })
}

/**
 * 工作协同-删除
 * @param {*} ids
 * @returns
 */
export function del(ids) {
  return request({
    url: 'api/aigov/collaboration/info/delete',
    method: 'post',
    data: ids
  })
}

export function saveMyWork(id) {
  return request({
    url: 'api/aigov/collaboration/info/saveMyWork',
    method: 'post',
    params: { id: id }
  })
}

export function cancelMyWork(id) {
  return request({
    url: 'api/aigov/collaboration/info/cancelMyWork',
    method: 'post',
    params: { id: id }
  })
}

export function urge(params) {
  return request({
    url: 'api/aigov/collaboration/info/smsUrge',
    method: 'post',
    data: params
  })
}

function getSms(params) {
  return request({
    url: 'api/aigov/collaboration/info/getSms',
    method: 'get',
    params
  })
}

export function findLinkFormValueById(params) {
  return request({
    url: '/collaborationLink/findById',
    method: 'get',
    params
  })
}

export default { signedList, unsignList, allList, editDetail, view, save, readFirst, del, saveMyWork, cancelMyWork, urge, getSms, findLinkFormValueById }
