import request from '@/utils/request'

/**
 * 分页搜索-所有工作协同操作记录
 * @param {*} queryParams
 * @returns
 */
export function list(queryParams) {
  return request({
    url: 'api/aigov/collaboration/handleLog/list',
    method: 'get',
    params: queryParams
  })
}

/**
 * 工作协同-发送、签收、转交、评论
 * @param {*} data
 * @returns
 */
export function handle(data) {
  return request({
    url: 'api/aigov/collaboration/handleLog/handle',
    method: 'post',
    data
  })
}

function queryUnsignedAssignees(params) {
  return request({
    url: 'api/aigov/collaboration/handleLog/queryUnsignedAssignees',
    method: 'get',
    params: params
  })
}
export default { list, handle, queryUnsignedAssignees }
export function queryAssignees(params) {
  return request({
    url: 'api/aigov/collaboration/handleLog/queryAssignees',
    method: 'get',
    params
  })
}

