import request from '@/utils/request'

export function queryTreeClass() {
  return request({
    url: `api/aigov/taskmanagement/class/getListTree`,
    method: 'post'
  })
}

export function add(data) {
  return request({
    url: 'api/aigov/taskmanagement/class/add',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/aigov/taskmanagement/class/del',
    method: 'post',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/aigov/taskmanagement/class/edit',
    method: 'post',
    data
  })
}
export function getClassesAll() {
  return request({
    url: 'api/aigov/taskmanagement/class/getClassesAll',
    method: 'get'
  })
}

export default { queryTreeClass, add, edit, del, getClassesAll }
