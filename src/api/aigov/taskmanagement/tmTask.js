import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/aigov/taskmanagement/task/add',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/aigov/taskmanagement/task/del',
    method: 'post',
    data: ids
  })
}

export function edit(data) {
  return request({
    // url: 'api/aigov/taskmanagement/task/edit',
  
    url: 'api/aigov/taskmanagement/task/updateProcess', 
    method: 'post',
    data
  })
}

export function getTaskAll() {
  return request({
    url: 'api/aigov/taskmanagement/task/getTaskAll',
    method: 'get'
  })
}

export function getTaskByType(type) {
  return request({
    url: 'api/aigov/taskmanagement/task/getTaskByType/' + type,
    method: 'get'
  })
}

export function getCatalogTree() {
  return request({
    url: 'api/aigov/taskmanagement/catalog/getCatalogTree',
    method: 'get'
  })
}

export default { add, edit, del, getTaskAll, getTaskByType, getCatalogTree }
