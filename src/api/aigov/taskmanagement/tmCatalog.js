import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/aigov/taskmanagement/catalog/add',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/aigov/taskmanagement/catalog/del',
    method: 'post',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/aigov/taskmanagement/catalog/edit',
    method: 'post',
    data
  })
}

export function allEnabled() {
  return request({
    url: 'api/aigov/taskmanagement/catalog/allEnabled',
    method: 'get'
  })
}

export function getCatalogTree() {
  return request({
    url: 'api/aigov/taskmanagement/catalog/getCatalogTree',
    method: 'get'
  })
}
export default { add, edit, del, allEnabled, getCatalogTree }
