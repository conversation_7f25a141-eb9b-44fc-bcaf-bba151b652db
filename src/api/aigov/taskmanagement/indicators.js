import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/aigov/taskmanagement/indicators/add',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/aigov/taskmanagement/indicators/del',
    method: 'post',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/aigov/taskmanagement/indicators/edit',
    method: 'post',
    data
  })
}

export default { add, edit, del }
