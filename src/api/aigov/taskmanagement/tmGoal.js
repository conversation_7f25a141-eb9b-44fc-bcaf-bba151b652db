import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/aigov/taskmanagement/goal/add',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/aigov/taskmanagement/goal/del',
    method: 'post',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/aigov/taskmanagement/goal/edit',
    method: 'post',
    data
  })
}

export default { add, edit, del }
