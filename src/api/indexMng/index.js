import request from '@/utils/request'

export function add(data) {
  return request({
    url: '/api/index/mng',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: '/api/index/mng',
    method: 'post',
    data
  })
}

export function list(params) {
  return request({
    url: '/api/index/mng',
    method: 'get',
    params
  })
}
export function createIndex(data) {
  return request({
    url: '/api/index/mng/createIndex?beanName=' + data.beanName,
    method: 'post'
  })
}
export function del(data) {
  return request({
    url: '/api/index/mng/delete',
    method: 'post',
    data
  })
}
export function delIndex(data) {
  return request({
    url: '/api/index/mng/deleteIndex?beanName=' + data.beanName,
    method: 'post'
  })
}
export function toDb(data) {
  return request({
    url: '/api/index/mng/toDb',
    method: 'post',
    data
  })
}
export default { add, edit, del }
