import request from '@/utils/request'
// 资产管理
/**
 * @method 资产处置列表
 * @params status(0待办 1未完结 2已办已完结)
 */
export function getDisposalsList(params) {
  return request({
    url: '/disposals/getDisposalsList',
    method: 'get',
    params
  })
}

// 资产管理
/**
 * @method 资产处置检索
 */
export function getDisposalsListRetrieve(params) {
  return request({
    url: '/disposals/getDisposalsListRetrieve',
    method: 'get',
    params
  })
}

// 资产处置:搜索全部、已办、待办
export function getAllOrRetrieve(params) {
  return request({
    url: '/disposals/getAllOrRetrieve',
    method: 'get',
    params
  })
}

