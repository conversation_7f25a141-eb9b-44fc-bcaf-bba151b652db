import request from '@/utils/request'
// 部门查询
export function cadreDept(params) {
  return request({
    url: '/api/cadreDept',
    method: 'get',
    params
  })
}
// 部门查询
export function cadreDeptLazy(params) {
  return request({
    url: '/api/cadreDept/lazy',
    method: 'get',
    params
  })
}
// 部门添加
export function cadreDeptAdd(data) {
  return request({
    url: '/api/cadreDept/add',
    method: 'post',
    data
  })
}
// 部门删除
export function cadreDeptDel(data) {
  return request({
    url: '/api/cadreDept/del',
    method: 'post',
    data
  })
}
// 部门编辑
export function cadreDeptEdit(data) {
  return request({
    url: '/api/cadreDept/edit',
    method: 'post',
    data
  })
}
// 部门排序
export function cadreSort(data) {
  return request({
    url: '/api/cadre/sort',
    method: 'post',
    data
  })
}
// 干部更新
export function cadreUpdate(data) {
  return request({
    url: '/api/cadre/update',
    method: 'post',
    data
  })
}
// 查询干部列表
export function cadre(params) {
  return request({
    url: '/api/cadre',
    method: 'get',
    params
  })
}
// 查询干部详情
export function findByNameAndIdCardNumber(params) {
  return request({
    url: '/api/cadre/findByNameAndIdCardNumber',
    method: 'get',
    params
  })
}
// 查询干部详情
export function cadreById(id) {
  return request({
    url: '/api/cadre/' + id,
    method: 'get'
  })
}
// 干部草稿
export function cadreDraft(data) {
  return request({
    url: '/api/cadre/draft',
    method: 'post',
    data
  })
}
// 干部通过
export function cadrePass(data) {
  return request({
    url: '/api/cadre/pass',
    method: 'post',
    data
  })
}
// 干部提交
export function cadreSubmit(data) {
  return request({
    url: '/api/cadre/submit',
    method: 'post',
    data
  })
}
// 干部删除
export function cadreDel(data) {
  return request({
    url: '/api/cadre/del',
    method: 'post',
    data
  })
}
// 干部退回
export function cadreBack(data) {
  return request({
    url: '/api/cadre/back',
    method: 'post',
    data
  })
}
// 干部退回-批量
export function cadreBatchBack(data) {
  return request({
    url: '/api/cadre/batchBack',
    method: 'post',
    data
  })
}
// 干部通过-批量
export function cadreBatchPass(data) {
  return request({
    url: '/api/cadre/batchPass',
    method: 'post',
    data
  })
}
// 干部提交-批量
export function cadreBatchSubmit(data) {
  return request({
    url: '/api/cadre/batchSubmit',
    method: 'post',
    data
  })
}
// 导入干部lrmx
export function upload(data, deptId, type) {
  return request({
    url: `/api/cadre/upload?deptId=${deptId}${type ? `&type=${type}` : ''}`,
    method: 'post',
    data
  })
}
// 导入干部lrmx 校验
export function uploadCheck(data) {
  return request({
    url: '/api/cadre/uploadCheck',
    method: 'post',
    data
  })
}
//  导入干部照片lrmx
export function uploadPhoto(data) {
  return request({
    url: '/api/cadre/uploadPhoto',
    method: 'post',
    data
  })
}
export function download(data) {
  return request({
    url: '/api/cadre/download',
    method: 'get',
    responseType: 'blob',
    params: data
  })
}

// 打印
export function pdf(params) {
  return request({
    url: '/api/cadre/pdf',
    method: 'get',
    params
  })
}

export default {
  cadreDept,
  cadreDeptLazy,
  cadreDeptAdd,
  cadreDeptDel,
  cadreDeptEdit,
  cadreSort,
  cadreUpdate,
  cadre,
  findByNameAndIdCardNumber,
  cadreById,
  cadreDraft,
  cadrePass,
  cadreSubmit,
  cadreDel,
  cadreBack,
  cadreBatchBack,
  cadreBatchPass,
  cadreBatchSubmit,
  upload,
  uploadCheck,
  uploadPhoto,
  download,
  pdf
}
