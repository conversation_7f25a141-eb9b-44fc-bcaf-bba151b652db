import request from '@/utils/request'
/**
 * 搜索行政服务列表
 * @param {}
 * @returns
 */
export function queryAdministrative(params) {
  return request({
    url: '/api/a04xzfw/queryAdministrative',
    method: 'get',
    params
  })
}
// 合法性审查报备分页搜索
export function queryPage(params) {
  return request({
    url: '/api/c04reviewscheme/queryPage',
    method: 'get',
    params
  })
}
// 党内合法性文件分页搜索
export function docQueryPage(params) {
  return request({
    url: '/api/c05normativedocuments/queryPage',
    method: 'get',
    params
  })
}
// 党内合法性文件统计搜索
export function queryPageList(params) {
  return request({
    url: '/api/c05normativedocuments/queryPageList',
    method: 'get',
    params
  })
}
export default { queryAdministrative, queryPage, docQueryPage, queryPageList }
