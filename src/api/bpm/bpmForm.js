import request from '@/utils/request'

// 流程提交/审批
export function complete(data) {
  return request({
    url: `/api/flowable/task/complete`,
    method: 'post',
    data
  })
}

// 流程拒绝
export function reject(data) {
  return request({
    url: `/api/flowable/task/reject`,
    method: 'post',
    data
  })
}

// 新增我的工作(领办)
export function myWorkSave(data) {
  return request({
    url: '/api/aigov/myWork/save',
    method: 'post',
    data
  })
}

// 删除我的工作(领办)
export function myWorkDel(data) {
  return request({
    url: '/api/aigov/myWork/del',
    method: 'post',
    params: data
  })
}

// 搜索我的工作(领办)
export function myWorkList(params) {
  return request({
    url: '/api/aigov/myWork/list',
    method: 'get',
    params
  })
}

// 搜索当前流程是否已领办(领办)
export function myWorkIsClaim(data) {
  return request({
    url: '/api/aigov/myWork/isClaim',
    method: 'get',
    params: data
  })
}
// processInstanceId去拿到taskId
export function getTaskId(params) {
  return request({
    url: '/api/bpm/handle/pageRouter',
    method: 'get',
    params
  })
}

// 暂存文档/保存草稿
export function formSubmit(data) {
  return request({
    url: '/api/form/formSubmit',
    method: 'POST',
    data
  })
}

// 获取特送所有可选择的节点
export function getExpressTaskList(taskId) {
  return request({
    url: `/api/flowable/task/expressTaskList/${taskId}`,
    method: 'GET'
  })
}

// 特送转办任务
export function sendTaskList(data) {
  return request({
    url: `/api/flowable/task/delegate`,
    method: 'POST',
    data
  })
}

// 获取重新恢复所有可选择的节点
export function getReactivateTaskList(procInstanceId) {
  return request({
    url: `/api/bpm/instance/reactivateTaskList/${procInstanceId}`,
    method: 'GET'
  })
}

// 恢复流程
export function reactivateProcess(data) {
  return request({
    url: `/api/bpm/instance/reactivateProcess`,
    method: 'POST',
    data
  })
}

// 回退首环节
export function returnFirst(data) {
  return request({
    url: `/api/flowable/task/returnFirst`,
    method: 'POST',
    data
  })
}
// 回退上一环节
export function returnLast(data) {
  return request({
    url: `/api/flowable/task/returnLast`,
    method: 'POST',
    data
  })
}

// 删除流程实例
export function delProcInstance(data) {
  return request({
    url: '/api/bpm/instance/delete',
    method: 'delete',
    params: data
  })
}
// 测回
export function revokeProcess(data) {
  return request({
    url: '/api/flowable/task/revokeProcess',
    method: 'POST',
    data
  })
}

// 已阅
export function saveHasRead(data) {
  return request({
    url: `/api/bpm/instance/saveHasRead`,
    method: 'POST',
    data
  })
}

// 判断系统收文登记提交时是否已存在相同文件名称的文件
export function getExistBt(params) {
  return request({
    url: '/api/aigov/document/sw/getExistBt',
    method: 'get',
    params
  })
}
