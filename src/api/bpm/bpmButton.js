import request from '@/utils/request'

export function list(queryParams) {
  return request({
    url: 'api/bpm/bpmButton/list',
    method: 'get',
    params: queryParams
  })
}

export function save(data) {
  return request({
    url: 'api/bpm/bpmButton/save',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/bpm/bpmButton/delete',
    method: 'post',
    data: ids
  })
}

/**
 * @method 根据实例id获取taskId
 * @params
 */
function webPageRouter(params) {
  return request({
    url: '/api/bpm/handle/webPageRouter',
    method: 'get',
    params
  })
}

export default { list, save, del, webPageRouter }
