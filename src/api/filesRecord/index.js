import request from '@/utils/request'

/**
 * @method 搜索文件档案目录树列表
 * @params token
 */
function getFileDataContent() {
  return request({
    url: '/api/directory',
    method: 'get'
  })
}

/**
 * @method 搜索文件档案列表
 * @params
 */
function getFileDataList(params) {
  return request({
    url: '/api/archives',
    method: 'get',
    params
  })
}

/**
 * @method 新增文件档案
 * @params
 */
function fileDataCreateOrUpdate(data) {
  return request({
    url: '/api/archives/createOrUpdate',
    method: 'post',
    data
  })
}

/**
 * @method 删除文件档案
 * @params ids
 */
function fileDataDelete(data) {
  return request({
    url: '/api/archives/delete',
    method: 'post',
    data
  })
}

export default { getFileDataContent, getFileDataList, fileDataCreateOrUpdate, fileDataDelete }
