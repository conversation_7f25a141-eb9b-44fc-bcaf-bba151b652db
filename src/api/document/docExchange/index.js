import request from '@/utils/request'
/**
 * @method 公文流转列表(待分发)
 * @params page   size  sort(id,asc)  creatorId  bt gwlx(公文类型(0:草稿,1:正式))
 */
function getGwlzList(params) {
  return request({
    url: '/api/aigov/document/gwlz/getGwlzList',
    method: 'get',
    params
  })
}

/**
 * @method 根据类型搜索模板信息
 * @params className:A07DocumentGwl
 */
export function getTemplateByClassName(params) {
  return request({
    url: '/api/dynamicForm/getTemplateByClassName',
    method: 'get',
    params
  })
}

/**
 * @method 用户公文流转列表
 * @params className:A07DocumentGwl
 */
function getGwlzUserList(params) {
  return request({
    url: '/api/aigov/document/gwlz/getGwlzUserList',
    method: 'get',
    params
  })
}

/**
 * @method 新增或修改公文流转
 * @data
 */
function saveGwlz(data) {
  return request({
    url: '/a07DocExchange/saveGwlz',
    method: 'post',
    data
  })
}

/**
 * @method 公文流转删除
 * @data
 */
function gwlzSc(data) {
  return request({
    url: '/api/aigov/document/gwlz/gwlzSc',
    method: 'post',
    data
  })
}

/**
 * @method 公文流转签收
 * @data
 */
function gwlzQs(data) {
  return request({
    url: '/api/aigov/document/gwlz/gwlzQs',
    method: 'post',
    data
  })
}

/**
 * @method 公文流转来文删除(已签收)
 * @data
 */
function gwlzLwSc(data) {
  return request({
    url: '/api/aigov/document/gwlz/gwlzLwSc',
    method: 'post',
    data
  })
}

/**
 * @method 公文流转撤回
 * @data
 */
function gwlzCh(data) {
  return request({
    url: '/api/aigov/document/gwlz/gwlzCh',
    method: 'post',
    data
  })
}

/**
 * @method 公文交换发文列表
 * @params status(0待办 1已办)
 */
function getDocExchangeFwList(params) {
  return request({
    url: '/a07DocExchange/getDocExchangeFwList',
    method: 'get',
    params
  })
}

/**
 * @method 公文交换收文列表
 * @params status(0待办 1已办)
 */
function getDocExchangeSwList(params) {
  return request({
    url: '/a07DocExchange/getDocExchangeSwList',
    method: 'get',
    params
  })
}

function getDeptTree(params) {
  return request({
    url: '/a07DocExchange/getDeptTree',
    method: 'get',
    params
  })
}

function getNum(params) {
  return request({
    url: '/a07DocExchange/getNum',
    method: 'get',
    params
  })
}
// 发文转公文交换
function fwToGw(params) {
  return request({
    url: '/a07DocExchange/fwToGw',
    method: 'post',
    data: params
  })
}

export function getSms(params) {
  return request({
    url: '/a07DocExchange/getSms',
    method: 'get',
    params
  })
}

function exportAll(params) {
  return request({
    url: '/a07DocExchange/exportAll',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export default {
  getGwlzList,
  getTemplateByClassName,
  getGwlzUserList,
  saveGwlz,
  gwlzSc,
  gwlzQs,
  gwlzLwSc,
  gwlzCh,
  getDocExchangeFwList,
  getDocExchangeSwList,
  getDeptTree,
  getNum,
  fwToGw,
  exportAll
}
