import request from '@/utils/request'
// 公文管理
/**
 * @method 公文管理列表
 * @params status(0待办 1已办) processDefinitionKey(公文类型)
 */
function getGwList(params) {
  return request({
    url: '/api/aigov/a07/getGwList',
    method: 'get',
    params
  })
}
/**
 * @method 获取流程图
 * @params
 */
function readXmlByDefKey(procDefKey) {
  return request({
    url: `/api/flowable/definition/readXmlByDefKey/${procDefKey}`,
    method: 'get'
  })
}
/**
 * @method 根据实例id获取taskId
 * @params
 */
function webPageRouter(params) {
  return request({
    url: '/api/bpm/handle/webPageRouter',
    method: 'get',
    params
  })
}

/**
 * @method 公文交换发文列表
 * @params status(0待办 1已办) processDefinitionKey(公文类型)
 */
function getDocExchangeFwList(params) {
  return request({
    url: '/a07DocExchange/getDocExchangeFwList',
    method: 'get',
    params
  })
}

/**
 * @method 公文交换收文列表
 * @params status(0待办 1已办) processDefinitionKey(公文类型)
 */
function getDocExchangeSwList(params) {
  return request({
    url: '/a07DocExchange/getDocExchangeSwList',
    method: 'get',
    params
  })
}

/**
 * @method 公文交换发文列表
 * @params status(0待办 1已办) processDefinitionKey(公文类型)
 */
function getOpenDoc(params) {
  return request({
    url: '/a07DocExchange/openDoc',
    method: 'get',
    params
  })
}

/**
 * @method 公文交换-待发文删除
 * @params status(0待办 1已办) processDefinitionKey(公文类型)
 */
function batchDel(ids) {
  return request({
    url: '/a07DocExchange/batchDel',
    method: 'post',
    data: JSON.stringify(ids)
  })
}

/**
 * @method 公文交换-来文签收
 * @params id 收文签收
 */
function sign(id) {
  return request({
    url: '/a07DocExchange/sign',
    method: 'post',
    data: id
  })
}

function revoke(id) {
  return request({
    url: '/a07DocExchange/revoke',
    method: 'post',
    data: id
  })
}

function getSignRecords(params) {
  return request({
    url: '/a07DocExchange/getSignRecords',
    method: 'get',
    params
  })
}

function smsUrge(params) {
  return request({
    url: '/a07DocExchange/smsUrge',
    method: 'post',
    data: params
  })
}

/**
 * 公文交换转单位收文
 * @param params
 * @returns {AxiosPromise}
 */
function doTransferToDeptOA(params) {
  return request({
    url: '/a07DocExchange/doTransferToDeptOA',
    method: 'post',
    params
  })
}

/**
 * 分页搜索发文
 * @param queryParams
 */
export function queryPageFw(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageFw',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索收文
 * @param queryParams
 */
export function queryPageSw(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageSw',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索领导批示
 * @param queryParams
 */
export function queryPageLdps(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageLdps',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索所有领厅内呈阅
 * @param queryParams
 */
export function queryPageTncy(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageTncy',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索所有会签
 * @param queryParams
 */
export function queryPageHq(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageHq',
    method: 'get',
    params: queryParams
  })
}

/**
 * 分页搜索逾期的领导批示件
 * @param queryParams
 */
export function queryPageLdpsYq(queryParams) {
  return request({
    url: '/api/aigov/document/ldps/queryPageLdpsYq',
    method: 'get',
    params: queryParams
  })
}

/**
 *退回公文
 */
export function sendback(thParam) {
  return request({
    url: '/a07DocExchange/sendback',
    method: 'post',
    params: thParam
  })
}

/**
 *依申请公开检索列表搜索
 */
export function queryPageXxgksq(queryParams) {
  return request({
    url: '/api/aigov/a07/queryPageXxgksq',
    method: 'get',
    params: queryParams
  })
}

export default {
  getGwList,
  readXmlByDefKey,
  webPageRouter,
  getDocExchangeFwList,
  getOpenDoc,
  getDocExchangeSwList,
  batchDel,
  sign,
  revoke,
  getSignRecords,
  smsUrge,
  doTransferToDeptOA,
  queryPageFw,
  queryPageSw,
  queryPageLdps,
  queryPageTncy,
  queryPageHq,
  queryPageLdpsYq,
  sendback,
  queryPageXxgksq
}
