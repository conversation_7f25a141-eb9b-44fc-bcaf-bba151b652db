// eslint-disable-next-line no-unused-vars
import request from '@/utils/request'

/**
 * @method 归档整理
 */
function save(params) {
  // return request({
  //   url: '/FileArchive/save',
  //   method: 'post',
  //   params
  // })
  var now = new Date()
  var time = now.getFullYear() + '-' + ((now.getMonth() + 1) < 10 ? '0' : '') + (now.getMonth() + 1) + '-' + (now.getDate() < 10 ? '0' : '') + now.getDate()

  if (params.id) {
    list[params.id - 1].IsArcAble = 3
    Object.keys(params).forEach(key => (list[params.id - 1][key] = params[key]))
    list[params.id - 1].archiveDate = time
    list[params.id - 1].enabled = 0
  } else {
    params.id = list.length
    params.archiveDate = time
    params.enabled = 0
    list.push(params)
  }
}

const list = [

  { id: 1, name: '浙江省教育厅关于同意成立浙江省银湖基础教育研究院的函', DispatchNo1: '浙教厅函〔2021〕283号', from: '发文', IsArcAble: 0, date: '2021-11-18', filename: '浙江省教育厅关于同意成立浙江省银湖基础教育研究院的函.docx', zwys: 2, ztwjsl: 2 },
  { id: 2, name: '浙江省教育厅关于报送《浙江省创建全国义务教育优质均衡发展县推进计划（2021-2030年）》的函', DispatchNo1: '浙教函〔2021〕92号', from: '发文', IsArcAble: 0, date: '2021-11-10', filename: '浙江省教育厅关于报送《浙江省创建全国义务教育优质均衡发展县推进计划（2021-2030年）》的函.docx', zwys: 2, ztwjsl: 3 },
  { id: 3, name: '浙江省教育厅关于浙江省盲人学校改建工程申请参照省重点建设项目管理的函', DispatchNo1: '浙教厅函〔2021〕276号', from: '发文', IsArcAble: 0, date: '2021-11-10', filename: '浙江省教育厅关于浙江省盲人学校改建工程申请参照省重点建设项目管理的函.docx', zwys: 3, ztwjsl: 2 },
  { id: 4, name: '浙江省教育厅办公室关于征集“双减”优秀实践案例的通知', DispatchNo1: '浙教办函〔2021〕290号', from: '发文', IsArcAble: 0, date: '2021-11-09', filename: '浙江省教育厅办公室关于征集“双减”优秀实践案例的通知.docx', zwys: 4, ztwjsl: 2 },
  { id: 5, name: '浙江省教育厅关于申报2022年省级政府投资项目年度计划的函', DispatchNo1: '浙教厅函〔2021〕275号', from: '发文', IsArcAble: 0, date: '2021-11-09', filename: '浙江省教育厅关于申报2022年省级政府投资项目年度计划的函.docx', zwys: 2, ztwjsl: 2 },
  { id: 6, name: '浙江省教育厅办公室关于公布省中小学劳动教育典型案例的通知', DispatchNo1: '浙教办基〔2021〕47号', from: '发文', IsArcAble: 0, date: '2021-11-10', filename: '浙江省教育厅办公室关于公布省中小学劳动教育典型案例的通知.docx', zwys: 3, ztwjsl: 2 },
  { id: 7, name: '浙江省教育厅办公室关于开展社区教育“能者为师”特色课程推介共享行动的通知', DispatchNo1: '浙教办函〔2021〕287号', from: '发文', IsArcAble: 0, date: '2021-11-10', filename: '浙江省教育厅办公室关于开展社区教育“能者为师”特色课程推介共享行动的通知.docx', zwys: 2, ztwjsl: 2 },
  { id: 8, name: '浙江省学位委员会关于进一步做好2020年审核增列的博士、硕士学位授予单位及其学位授权点建设和审核增列博士硕士学位授权点建设的通知', DispatchNo1: '浙学位〔2021〕1号', from: '发文', IsArcAble: 0, date: '2021-11-15', filename: '浙江省学位委员会关于进一步做好2020年审核增列的博士、硕士学位授予单位及其学位授权点建设和审核增列博士硕士学位授权点建设的通知.docx', zwys: 2, ztwjsl: 2 },
  { id: 9, name: '浙江省教育厅关于申请设立浙江师范大学堪萨斯大学联合教育学院的函', DispatchNo1: '浙教函〔2021〕88号', from: '发文', IsArcAble: 0, date: '2021-11-17', filename: '浙江省教育厅关于申请设立浙江师范大学堪萨斯大学联合教育学院的函.docx', zwys: 4, ztwjsl: 2 },
  { id: 10, name: '浙江省教育厅关于浙江省2020-2021学年普通高校本专科生国家奖学金评审情况的函', DispatchNo1: '浙教函〔2021〕90号', from: '发文', IsArcAble: 0, date: '2021-11-19', zwys: 2, ztwjsl: 2 }
]
/**
 * 未归档
 * @param params
 */
function queryList(params) {
  console.log(params)
  const content = list.filter(item => {
    let flag = item.IsArcAble !== 3
    if (params.IsArcAble !== '') {
      flag &= (item.IsArcAble === Number(params.IsArcAble))
    }
    if (params.from !== '') {
      flag &= (item.from === params.from)
    }
    return flag
  })
  const result = {
    total: content.length,
    content: content
  }
  console.log(result)
  return result
}

/**
 * @method 搜索列表
 * @param params
 */
function queryArchiveList(params) {
  // return request({
  //   url: '/FileArchive/queryList',
  //   method: 'get',
  //   params
  // })
  const content = list.filter(item => {
    let flag = item.IsArcAble === 3
    if (params.beginDate && params.beginDate !== '') {
      flag &= (item.archiveDate >= params.beginDate)
    }
    if (params.endDate && params.endDate !== '') {
      flag &= (item.archiveDate <= params.endDate)
    }
    if (params.enabled && params.enabled !== '') {
      flag &= (item.enabled === Number(params.enabled))
    }
    return flag
  })
  const result = {
    total: content.length,
    content: content
  }
  console.log(result)
  return result
}

/**
 * @method 标记
 */
function mark(params) {
  // return request({
  //   url: '/FileArchive/mark',
  //   method: 'post',
  //   params
  // })
  list.filter(item => {
    return params.IDs.indexOf(item.id) > -1
  }).forEach(item => {
    item.IsArcAble = params.isAble
  })
}

function del(ids) {
  // return request({
  //   url: '/FileArchive/del',
  //   method: 'delete',
  //   params
  // })
  list.map((item, i) => {
    if (ids.indexOf(item.id) > -1) {
      list.splice(i, 1)
    }
  })
}

function yijiao(params) {
  // return request({
  //   url: '/FileArchive/yijiao',
  //   method: 'post',
  //   params
  // })
  list.filter(item => {
    return item.id === params.id
  }).forEach(item => {
    item.enabled = 1
  })
}
export default {
  save,
  queryList,
  queryArchiveList,
  mark,
  del,
  yijiao
}
