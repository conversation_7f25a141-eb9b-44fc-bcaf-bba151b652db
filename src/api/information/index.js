import request from '@/utils/request'

// 获取电子公告列表(查阅或管理)
export function getManagerList(queryParams) {
  return request({
    url: '/api/aigov/a06/getManagerList',
    method: 'get',
    params: queryParams
  })
}
// 删除电子公告列表
export function delManager(data) {
  return request({
    url: '/api/aigov/a06/delete',
    method: 'POST',
    data
  })
}
// 获取审批列表
export function getApprovalList(queryParams) {
  return request({
    url: '/api/aigov/a06/getApprovalList',
    method: 'get',
    params: queryParams
  })
}
// 获取政策文件列表
export function getPolicyDocumentList(queryParams) {
  return request({
    url: '/api/aigov/a07/getPolicyDocumentList',
    method: 'get',
    params: queryParams
  })
}

// 获取动态信息
export function information(params) {
  return request({
    url: '/api/aigov/a06',
    method: 'get',
    params
  })
}
// 根据id获取详情
export function getDetail(data) {
  return request({
    url: '/api/aigov/a06/findById',
    method: 'POST',
    data
  })
}
// 改变列表阅读状态
export function updateState(data) {
  return request({
    url: '/api/aigov/a06/read',
    method: 'POST',
    data
  })
}

// 根据系统获取模块列表
export function getModuleList(data) {
  return request({
    url: window.g.apiBacklogUrl + '/api/backlog/getModuleList',
    method: 'post',
    data
  })
}
// 待办
export function getBacklogList(data) {
  return request({
    url: window.g.apiBacklogUrl + '/api/backlog/getBacklogList',
    method: 'post',
    data
  })
}
// 获取模块的待办数量
export function getModulesNum(data) {
  return request({
    url: window.g.apiBacklogUrl + '/api/backlog/getModuleCountByWaitBacklog',
    method: 'get',
    params: data
  })
}

// 根据实例id获取taskId
export function webPageRouter(params) {
  return request({
    url: '/api/bpm/handle/webPageRouter',
    method: 'get',
    params
  })
}

export function getReceiveList(params) {
  return request({
    url: '/api/aigov/a06/getReceiveList',
    method: 'get',
    params
  })
}

export function revoke(params) {
  return request({
    url: '/api/aigov/a06/revoke',
    method: 'post',
    data: params
  })
}

export function topping(params) {
  return request({
    url: '/api/aigov/a06/topping',
    method: 'post',
    data: params
  })
}

export function setInvisibleById(params) {
  return request({
    url: '/api/aigov/a06/setInvisibleById',
    method: 'post',
    data: params
  })
}

export function a06NewsReceiveList(params) {
  return request({
    url: '/api/aigov/a06/a06NewsReceiveList',
    method: 'get',
    params
  })
}

export function batchComplete(data) {
  return request({
    url: '/api/flowable/task/batchComplete',
    method: 'post',
    data
  })
}
// 获取批量办理待办任务
export function batchHandleTask(data) {
  return request({
    url: '/api/flowable/task/batchHandleTask',
    method: 'post',
    data
  })
}

// 工作台版本切换获取版本参数
export function findByUserName(data) {
  return request({
    url: '/api/aigov/theme/findByUserName',
    method: 'GET',
    params: data
  })
}

// 工作台版本切换修改版本参数
export function batchHandleTaskAdd(data) {
  return request({
    url: '/api/aigov/theme/add',
    method: 'post',
    data
  })
}
