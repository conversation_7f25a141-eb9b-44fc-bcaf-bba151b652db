<template>
  <div v-if="variables" class="scroll-bar">
    <div v-loading="loading" class="app-container" :style="{ height: this.$route.query.taskId?'calc(100% - 53px)':'100%'}">
      <div class="header">
        <div class="left">
          <div v-if="variables.processJson">当前环节：<span>{{ variables.processJson.taskName }}</span></div>
        </div>
        <div class="title">{{ detailData.name }}</div>
        <div class="btnBox">
          <div class="fillingStatus" @click="openLZJL">
            流转记录
          </div>
        </div>
      </div>
      <el-tree
        ref="tree"
        class="tree"
        :data="detailData.classes"
        node-key="classId"
        :props="{ children: 'children', label: 'name'}"
      >
        <div slot-scope="{ data }" class="custom-tree-node">
          <template v-if="data.classId">
            <span class="title"><i style="font-style: normal;margin-right: 6px;">{{ data.indexPath }}</i>{{ data.name }}</span>
          </template>
          <template v-else>
            <div ref="task" class="taskCard" @click="showEdit(data)">
              <div class="taskCard-title">
                <div class="taskCard-title-text">
                  <span class="taskCard-title-text-index">{{ data.indexPath }}</span>
                  {{ data.name }}
                </div>
                <el-popover
                  placement="right"
                  width="400"
                  trigger="hover"
                  @show="taskHistory(data)"
                >
                  <workRecordHistory v-model="data.history" v-loading="data.loading" />
                  <el-link slot="reference" type="primary" :underline="false" class="taskCard-title-workRecord">工作记实</el-link>
                </el-popover>
              </div>
              <div class="taskCard-tag">
                <el-tooltip v-if="data.leader" effect="light" :content="'领导:'+ data.leader" placement="top-start">
                  <el-tag size="mini" class="primary tag">领导:{{ data.leader }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="data.depts" effect="light" :content="'主办:'+ data.depts" placement="top-start">
                  <el-tag size="mini" class="primary tag">主办:{{ data.depts }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="data.assistDepts" effect="light" :content="'协办:'+ data.assistDepts" placement="top-start">
                  <el-tag size="mini" type="warning" class="warning tag">协办:{{ data.assistDepts }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="data.responsible" effect="light" :content="'经办:'+ data.responsible" placement="top-start">
                  <el-tag size="mini" type="success" class="success tag">经办:{{ data.responsible }}</el-tag>
                </el-tooltip>
              </div>

              <div class="taskCard-source hidden">
                <el-tooltip v-for="(item,index) in data.content" :key="index" :style="{'max-width':(100/data.content.length)+'%'}" effect="light" :content="item" placement="top-start">
                  <span class="taskCard-source-item">
                    {{ item }}
                  </span>
                </el-tooltip>
              </div>
              <div class="taskCard-content hidden">
                <div class="taskCard-content-label">进度计划：</div>
                <div class="taskCard-content-value">{{ data.plan }}</div>
              </div>
              <div class="taskCard-content">
                <div class="taskCard-content-label">工作进展：</div>
                <div class="taskCard-content-value">{{ data.result }}</div>
              </div>
              <div class="taskCard-content hidden">
                <div class="taskCard-content-label">备注：</div>
                <div class="taskCard-content-value"> {{ data.remark }}</div>
              </div>
              <div class="taskCard-content">
                <div class="taskCard-content-label">工作进度：</div>
                <div class="taskCard-content-value"><el-progress :percentage="data.progress?data.progress:0" :stroke-width="10" /></div>
              </div>
              <div class="taskCard-content hidden">
                <div class="taskCard-content-label">附件：</div>
                <div class="taskCard-content-value">
                  <files v-if="data.fj && data.fj.length>0" v-model="data.fj" />
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-tree>
    </div>
    <div v-show="this.$route.query.taskId" class="footer">
      <div class="btns">
        <el-button type="primary" size="small" @click=" TJ()">办理</el-button>
      </div>
    </div>
    <myFmGenerateFormBox v-show="false" ref="myFmGenerateFormBox" class="myFmGenerateFormBox" :data="variables" />
    <!-- 提交/审核/拒绝 -->
    <el-dialog
      v-if="variables.processJson"
      :close-on-click-modal="false"
      :title="variables.processJson.taskName"
      :visible.sync="examineShow"
      width="800px"
    >
      <dialogExamine ref="tjdialog" :process-json="variables.processJson" :pre-sign="variables.preSign" :task-data="taskData" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="examineShow = false">取 消</el-button>
        <el-button v-if="TJOrJJ==='TJ'" :loading="tjLoading" type="primary" @click="tjgetData('TJ')">确 定</el-button>
      </span>
    </el-dialog>
    <el-drawer
      title="处理单"
      :visible.sync="drawer"
      direction="rtl"
      custom-class="taskFeedbackDrawer"
    >
      <div class="FormBox">
        <myFmGenerateFormBox class="myFmGenerateFormBox" :data="variables" />
      </div>
    </el-drawer>
    <el-dialog
      :visible.sync="editDialogVisible"
      width="80%"
      :modal="false"
      custom-class="workRecordEditDialog"
      :close-on-click-modal="false"
    >
      <el-form ref="editForm" :model="editForm" label-width="90px" class="editForm">
        <div class="editForm-row editForm-title">
          <el-tag v-if="editForm.finished" class="editForm-title-tag" type="danger" size="mini">办结销号</el-tag>
          <span class="editForm-title-index">{{ editForm.indexPath }}</span>
          {{ editForm.name }}
        </div>
        <el-row>
          <el-col :span="8">
            <div class="editForm-row editForm-tag">
              <el-form-item label="责任人:">
                <el-tooltip v-if="editForm.leader" effect="light" :content="'领导:'+ editForm.leader" placement="top-start">
                  <el-tag size="mini" class="primary tag">领导:{{ editForm.leader }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="editForm.depts" effect="light" :content="'主办:'+ editForm.depts" placement="top-start">
                  <el-tag size="mini" class="primary tag">主办:{{ editForm.depts }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="editForm.assistDepts" effect="light" :content="'协办:'+ editForm.assistDepts" placement="top-start">
                  <el-tag size="mini" type="warning" class="warning tag">协办:{{ editForm.assistDepts }}</el-tag>
                </el-tooltip>
                <el-tooltip v-if="editForm.responsible" effect="light" :content="'经办:'+ editForm.responsible" placement="top-start">
                  <el-tag size="mini" type="success" class="success tag">经办:{{ editForm.responsible }}</el-tag>
                </el-tooltip>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="editForm-row editForm-source">
              <el-form-item label="指标来源:">
                <el-tooltip v-for="(item,index) in editForm.content" :key="index" :style="{'max-width':'calc( '+(100/editForm.content.length)+'%'+' - 6px)'}" effect="light" :content="item" placement="top-start">
                  <span class="editForm-source-item">
                    {{ item }}
                  </span>
                </el-tooltip>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="8">
            <el-row>
              <el-col :span="12">
                <el-form-item label="工作进度:" prop="progress" :rules="[{ required: true, message: '请输入工作进度' }]">
                  <el-input
                    v-model.number="editForm.progress"
                  >
                    <span slot="suffix">%</span>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label-width="12px">
                  <el-checkbox v-model="editForm.finished">是否办结</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-form-item label="进度计划:" prop="plan">
          <el-input
            v-model="editForm.plan"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          />

        </el-form-item>
        <el-form-item label="工作进展:" prop="result" :rules="[{ required: true, message: '请输入工作进展' }]">
          <div class="editForm-result-history clearfix">
            <template v-for="item in onlySelfHistory">
              <el-tooltip v-if="item.result" :key="item.id" effect="light" :content="item.result" placement="top-start">
                <div class="editForm-result-history-item">
                  <span class="editForm-result-history-item-date">{{ moment(item.updateTime).format('MM-DD') }}</span>
                  <span class="editForm-result-history-item-btn" @click="editForm.result+=item.result">填充</span>
                </div>
              </el-tooltip>
            </template>
          </div>
          <el-input
            v-model="editForm.result"
            type="textarea"
            :rows="3"
            placeholder="请输入"
            maxlength="500"
            show-word-limit
          />

        </el-form-item>
        <el-row>

          <el-col :span="12">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="editForm.remark"
              />

            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件:" prop="fj">
              <fjUploadFile v-model="editForm.fj" :multiple="true" :min-num="0" size="small" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :loading="editLoading" @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="editLoading" @click="saveTask">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import taskmanagementApi from '@/api/taskmanagement/index.js'
import files from '@/views/document/taskManagementNew/comm/files.vue'
import workRecordHistory from '@/views/document/taskManagementNew/comm/workRecordHistory.vue'
import btnDatas from '../mixin/btnDatas'
import fjUploadFile from '@/views/document/taskManagementNew/comm/fjUploadFile.vue'
export default {
  components: {
    files,
    workRecordHistory,
    fjUploadFile
  },
  mixins: [btnDatas],
  props: {},
  data() {
    return {
      procInstId: null,
      loading: true,
      saveLoading: false,
      detailData: {
        catalogId: null,
        name: '',
        classes: []
      },
      examineShow: false,
      processJson: null,
      tjLoading: false,
      taskData: {
        bpmProcessKey: this.variables.formSkin,
        taskName: this.variables.processJson ? this.variables.processJson.taskName : null
      },
      drawer: false,
      editDialogVisible: false,
      editForm: {},
      editLoading: false,
      onlySelfHistory: []
    }
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    this.getDeatil()
  },
  methods: {
    // 获取详情
    getDeatil() {
      this.loading = true
      taskmanagementApi.getByProcInstId({ procInstId: this.$route.query.procInsId || this.variables.processJson.processInstanceId }).then(res => {
        res.classes = this.generateIndexPath(res.classes)
        this.detailData = res
        this.loading = false

        const that = this
        setTimeout(() => {
          for (const id in that.$refs.tree.store.nodesMap) {
            setTimeout(() => {
              that.$refs.tree.store.nodesMap[id].expanded = true
            }, 0)
          }
        }, 1000)
      })
    },
    // 生成索引路径
    generateIndexPath(data, parentPath = '') {
      return data.map((item, index) => {
        const currentPath = parentPath ? `${parentPath}.${item.sort}` : `${item.sort}`
        if (item.content) {
          var cleanedText = item.content.replace(/[\s]+/g, '')

          // 以；分割为数组
          var resultArray = cleanedText.split('；')
          item.content = resultArray
        }
        return {
          ...item,
          treeId: item.taskId ? item.taskId : item.classId,
          indexPath: currentPath,
          children: item.children ? this.generateIndexPath(item.children, currentPath) : [],
          disabled: item.taskId ? !item.responsible : false
        }
      })
    },
    // 任务历史
    taskHistory(data) {
      if (!data.history) {
        this.$set(data, 'loading', true)
        data['loading'] = false
        taskmanagementApi.history({ taskId: data.taskId }).then(res => {
          this.$set(data, 'history', res || [])
          this.$set(data, 'loading', false)
        })
      }
    },
    openLZJL() {
      this.drawer = true
    },
    // 显示编辑
    showEdit(data) {
      if (this.$route.query.taskId) {
        this.getOnlySelfHistory(data)
        this.editForm = JSON.parse(JSON.stringify(data))
        this.editForm.finished = !!this.editForm.finished
        this.editDialogVisible = true
      }
    },
    // 任务历史
    getOnlySelfHistory(data) {
      taskmanagementApi.history({ taskId: data.taskId, onlySelf: true }).then(res => {
        this.onlySelfHistory = res || []
      })
    },
    // 保存任务
    saveTask() {
      this.$refs.editForm.validate((valid) => {
        if (!valid) {
          return
        }
        this.saveLoading = true
        var params = [{ ...this.editForm, catalogId: this.detailData.catalogId, finished: this.editForm.finished ? 1 : 0 }]
        taskmanagementApi.save(params).then(res => {
          this.saveLoading = false
          this.$message.success('保存成功')
          this.editForm.isFilled = true
          // 更新树组件中的任务数据
          const updateTreeData = (data) => {
            for (let i = 0; i < data.length; i++) {
              if (data[i].taskId === this.editForm.taskId) {
                // 将修改后的数据赋值给树节点
                Object.assign(data[i], this.editForm)
                return true
              }
              if (data[i].children && data[i].children.length > 0) {
                if (updateTreeData(data[i].children)) {
                  return true
                }
              }
            }
            return false
          }
          updateTreeData(this.detailData.classes)
          this.editDialogVisible = false
        }).catch(() => {
          this.saveLoading = false
        })
      })
      // 关闭弹窗
    }
  }
}
</script>
<style lang="scss" scoped>
 @import "@/assets/css/scss/taskManagementNew2.scss";
.scroll-bar {
   .app-container{
     .header{
      justify-content: space-between;
      .left{
        font-size: 14px;
        span{
          color: rgba(42, 120, 255, 1);
        }
      }
      .title{
        width:auto;
        padding-left:0;
      }
      .btnBox{
        font-size: 14px;
        .fillingStatus{
          color: rgba(42, 120, 255, 1);
          cursor: pointer;
        }
      }
     }
    }
    .editForm{
      &-row{
        margin-bottom: 12px;
      }
      &-title{
        font-size: 13px;
        padding-right: 50px;
        line-height: 24px;
        // overflow: hidden;
        // white-space: nowrap;
        // text-overflow: ellipsis;

        &-tag{
          margin-right: 5px;
        }
        &-index{
          margin-right: 5px;
        }
        &-workRecord{
          font-size: 12px;
          margin-left: 6px;
          white-space: nowrap;
        }
      }
      &-tag{
        width:100%;
        .tag{
          max-width: calc(25% - 4px);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        span{
          margin-left: 5px;
          vertical-align: middle;
          &:first-child{
            margin-left: 0;
          }
        }
        .primary{
          border-color: #1890ff;
        }
        .warning{
          color: #e9a601;
          border-color: #e9a601;
        }
        .success{
          border-color: #52C41A;
        }
      }
      &-source{
          font-size: 12px;
          color: #13CE66;
          &-item{
            line-height: 14px;
            display: inline-block;
            padding: 3px 6px;
            border-radius: 12px;
            border: 1px solid #13CE66;
            white-space: nowrap;
            margin-left: 6px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: middle;
          }
      }
      &-result-history{
        margin: 8px 0 0;
        &-item{
          font-size: 12px;
          padding: 2px 10px;
          line-height: 20px;
          float: left;
          background-color: rgba(42, 120, 255, 0.12);
          border: rgba(191, 212, 250, 0.18);
          margin-right: 5px;
          border-radius: 100px;
          margin-bottom: 10px;
          &-date{
            color: rgba(48, 49, 51, 1);
          }
          &-btn{
             cursor: pointer;
            margin-left: 3px;
            color: rgba(42, 120, 255, 1);
          }
        }
      }
    }
   .footer{
    .btns{
      width: 100%;
      padding-right:0;
    }
   }
   .FormBox{
    padding:0 10px;
     .myFmGenerateFormBox ::v-deep .titleBtns{
      display: none;
     }
   }
  }
</style>
<style lang="scss">
.taskFeedbackDrawer{
  .el-drawer__header{
    padding: 16px 10px;
    border-bottom: 1px solid rgba(216, 216, 216, 1);
    margin-bottom: 10px;
  }
}
</style>
<style lang="scss">
 .workRecordEditDialog{
   .el-dialog__header{
    padding: 0;
    padding-bottom: 0;
  }
  .el-dialog__body{
    padding-top: 20px;
  }
  .el-dialog__footer{
    text-align: center;
  }
 }
</style>
