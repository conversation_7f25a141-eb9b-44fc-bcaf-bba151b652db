<script>
import preview from '@/views/handle/Template/comm/preview/index.vue'
import myFmGenerateFormBox from '@/views/handle/Template/comm/myFmGenerateFormBox/index.vue'
import { EventBus } from '@/utils/event-bus'
import a07DocumentDbjApi, { detail } from '@/api/a07DocumentDbj'

export default {
  name: 'View',
  components: { myFmGenerateFormBox, preview },
  data() {
    return {
      contentStute: 'middleScreen',
      relation: [],
      variables: null,
      showForm: true
    }
  },
  created() {
    this.initContentModule()
  },
  mounted() {
    this.getRelationFlow()
    this.getdbList()
    const _this = this
    window.onresize = function temp() {
      _this.initContentModule()
    }
  },
  methods: {
    initContentModule() {
      if (document.body.clientWidth < 1600) {
        this.contentStute = 'smallScreen'
      } else if (document.body.clientWidth > 1707) {
        this.contentStute = 'largeScreen'
      } else {
        this.contentStute = 'middleScreen'
      }
    },
    getRelationFlow() {
      a07DocumentDbjApi.sw({ id: this.$route.query.id }).then((res) => {
        this.relation = [res]
      })
    },
    getdbList() {
      detail({ id: this.$route.query.id }).then(res => {
        this.variables = res
        const formData = res.formData
        EventBus.$emit('eventFiles', formData.storageId.split(',').map(item => ({
          storageId: item,
          type: 'db'
        })))
      })
    },
    onChange(action) {
      this.showForm = action === -1
    },
    OnRelationDialog(item) {
      const text = this.$router.resolve({
        path: '/handle' + '?procInsId=' + item.procInstId
      })
      window.open(text.href, '_blank')
    }
  }
}
</script>

<template>
  <div class="app-container">
    <div class="header">
      <ul class="right-menu pull_right" />
    </div>
    <div class="content" :class="contentStute">
      <div class="content_left content_left_no">
        <div class="content_left_box">
          <div
            v-show="showForm || contentStute !== 'smallScreen'"
            class="content_left_myFmGenerateFormBox"
          >
            <myFmGenerateFormBox
              v-if="variables"
              ref="myFmGenerateFormBox"
              class="myFmGenerateFormBox"
              :data="variables"
            />
          </div>
          <div
            class="content_left_preview"
            :class="!showForm ? 'content_left_preview_show' : ''"
          >
            <preview
              class="preview"
              :content-stute="contentStute"
              @on-change="onChange"
            />
          </div>
        </div>
      </div>

      <div class="content_right">
        <div class="box">
          <div ref="fraStyle" class="main" style="height: 100%">
            <div class="content_right_top">
              <!-- 相关文件 -->
              <div class="content_right_top_box">
                <div class="title"><span><i class="el-icon-link" />相关文件</span></div>
                <ul class="relation">
                  <div
                    class="empty"
                  >
                    暂无相关文件！
                  </div>
                </ul>
              </div>
              <!-- 相关文件 -->
              <div class="content_right_top_box" style="margin-top: 100px;">
                <div class="title"><span><i class="el-icon-link" />相关事项</span></div>
                <ul class="relation">
                  <div
                    v-if="relation.length === 0"
                    class="empty"
                  >
                    暂无相关文件！
                  </div>
                  <li
                    v-for="(item, index) in relation"
                    :key="index"
                    @click="OnRelationDialog(item)"
                  >
                    <div class="icon">
                      <i class="el-icon-s-management" />
                    </div>
                    <div class="text">{{ item.title }}</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<style lang="scss" scoped>
.app-container {
  padding: 0;
  background: #f6f6fc;
  width: 100%;
  height: 100%;
  font-size: 15px;
  min-width: 1100px;

  .header {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #dddddd;
    font-size: 16px;
    padding: 0 15px;
    background: #0b6fe9;
    color: #fff;
    .currentLink {
      margin-left: 10px;
      font-size: 14px;
    }
    .right-menu {
      height: 100%;
      font-size: 15px;
      li,
      ::v-deep .el-dropdown {
        height: 100%;
        display: inline-block;
        padding: 0 15px;
        cursor: pointer;
        & > li {
          padding: 0;
        }
        .right-menu-name {
          color: #fff;
        }
        i {
          margin-right: 5px;
          color: #fff;
        }
        &:hover {
          background: #567ab1;
        }
      }
    }
  }
  & > .content {
    height: calc(100% - 50px);
    padding: 15px 8px;
    width: 100%;
    .content_left {
      height: 100%;
      float: left;
      &::-webkit-scrollbar {
        display: none;
      }
      &::scrollbar {
        display: none;
      }
      .box {
        overflow: hidden;
        width: 100%;
        height: 100%;
        .main {
          overflow-y: auto;
          width: 100%;
          height: 100%;
          &::-webkit-scrollbar {
            display: none;
          }
          &::scrollbar {
            display: none;
          }
        }
      }
    }
    &.smallScreen {
      .content_left {
        width: calc(100% - 470px);
        &.content_left_no {
          height: 100%;
          padding: 0 10px;
          .content_left_box {
            border-radius: 8px;
            background: #fff;
            overflow: hidden;
            height: 100%;
            .content_left_myFmGenerateFormBox {
              padding: 15px 15px 0;
              height: calc(100% - 40px);
            }
            .content_left_preview {
              height: 30px;
              position: relative;
              top: 10px;
              &.content_left_preview_show {
                height: 100%;
                position: relative;
                top: 0px;
              }
            }
          }
        }
      }
      .content_right {
        width: 470px;
      }
    }
    &.middleScreen {
      .content_left {
        width: calc(100% - 470px);
        &.content_left_no {
          height: 100%;
          .content_left_box {
            height: 100%;
            overflow: auto;
            .content_left_myFmGenerateFormBox,
            .content_left_preview {
              width: 480px;
              float: left;
              padding-left: 8px;
              padding-right: 8px;
              height: 100%;
            }
            .myFmGenerateFormBox,
            .preview {
              background: #fff;
              border-radius: 8px;
            }
            .content_left_preview {
              width: calc(100% - 480px);
            }
            .myFmGenerateFormBox {
              padding: 15px;
            }
          }
        }
      }
      .content_right {
        width: 470px;
      }
    }
    &.largeScreen {
      .content_left {
        width: 72%;
        &.content_left_no {
          height: 100%;
          .content_left_box {
            height: 100%;
            overflow: auto;
            .content_left_myFmGenerateFormBox,
            .content_left_preview {
              width: 40%;
              float: left;
              padding-left: 8px;
              padding-right: 8px;
              height: 100%;
            }
            .myFmGenerateFormBox,
            .preview {
              background: #fff;
              border-radius: 8px;
            }
            .content_left_preview {
              width: 60%;
            }
            .myFmGenerateFormBox {
              padding: 15px;
            }
          }
        }
      }
      .content_right {
        width: 28%;
      }
    }
    .content_right {
      height: 100%;
      float: left;
      &::-webkit-scrollbar {
        display: none;
      }
      &::scrollbar {
        display: none;
      }
      .box {
        overflow: hidden;
        width: 100%;
        height: 100%;

        .main {
          overflow-y: auto;
          width: 100%;
          height: 100%;
          &::-webkit-scrollbar {
            display: none;
          }
          &::scrollbar {
            display: none;
          }
        }
      }
    }
    .content_right {
      padding: 0 10px;
      .title {
        padding: 15px;
        font-size: 17px;
        i {
          color: #2a78ff;
          margin-right: 5px;
        }
      }
      .box {
        background: #fff;
        border-radius: 8px;
        // box-shadow: 2px 0px 30px 0px rgba(36, 34, 34, 0.1);
        .main {
          padding: 0 15px;
          .title {
            padding: 15px 0;
          }
          .steps {
            padding: 10px 0;
            max-height: 55%;
            width: 100%;
            border-top: 1px solid #eeeeee;
            flex-flow: column;
            display: flex;
            .content {
              height: auto;
            }
            ::v-deep .el-row {
              height: auto !important;
              .el-col {
                height: auto !important;
              }
            }
            .tjdialog_box {
              height: calc(100% - 100px);
              overflow: auto;
              &::-webkit-scrollbar {
                display: none;
              }
              &::scrollbar {
                display: none;
              }
            }
            .tjdialog_btn {
              border-top: 1px solid #eee;
              text-align: right;
              padding-top: 7px;
            }
          }
          .content_right_top{
            min-height: calc(30% - 160px);
            overflow: auto;
            &::-webkit-scrollbar {
              display: none;
            }
            &::scrollbar {
              display: none;
            }
            .content_right_top_box{
              height: 50%;
              .title{
                font-size: 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .el-icon-circle-plus-outline{
                  font-size: 18px;
                  cursor: pointer;
                }
              }
            }
          }
          .relation {
            height: calc(100% - 50px);
            overflow: auto;
            li {
              display: flex;
              margin-bottom: 15px;
            }
            .icon {
              width: 15px;
              margin-right: 8px;
              i {
                color: #2a78ff;
                margin-right: 5px;
              }
            }
            .text {
              color: #484848;
              cursor: pointer;
            }
            .empty {
              width: 100%;
              height: 100%;
              font-size: 16px;
              color: #888;
              letter-spacing: 10px;
              display: flex;
              align-items: center;
              justify-content: space-evenly;
            }
          }
          .superviseAndHandle {
            height: calc(100% - 50px);
            overflow: auto;

            .superviseAndHandle-item {
              width: 100%;
              display: flex;
              margin-bottom: 6px;
              background: rgba(243, 247, 254, 1);
              padding: 8px 10px;
            }
            .addDb{
              width: 100%;
              background: rgba(243, 247, 254, 1);
              padding: 10px;
              color:#1876E9;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              .el-icon-circle-plus-outline{
                font-size:14px;
              }
              .addDb-text{
                margin-left: 5px;
                font-size: 12px;
                font-weight: bold;
              }
            }
            .icon {
              width: 15px;
              margin-right: 8px;
              i {
                color: #2a78ff;
                margin-right: 5px;
              }
            }
            .text {
              width: 100%;
              color: #484848;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: space-between;
              .dbLink.is-disabled{
                color: #606266;
              }
            }
            .empty {
              width: 100%;
              height: 100%;
              font-size: 16px;
              color: #888;
              letter-spacing: 10px;
              display: flex;
              align-items: center;
              justify-content: space-evenly;
            }
          }

        }
      }
    }
  }
}
::v-deep .el-date-editor.el-input {
  width: 100%;
}
::v-deep .bjs-powered-by {
  display: none !important;
}
::v-deep .tjdialog_box .steps {
  overflow: hidden;
}
</style>
