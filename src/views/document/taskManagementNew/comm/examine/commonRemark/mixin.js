import { comments, create } from '@/api/system/user'
export default {
  data() {
    return {
      remarkList: [],
      remarkListShow: false
    }
  },
  created() {
    this.getQuery()
  },
  methods: {
    // 获取常用意见列表并显示
    getQuery() {
      comments().then((res) => {
        this.remarkList = res
      })
    },
    // 获取常用意见列表并显示
    showCommonRemark() {
      this.remarkListShow = !this.remarkListShow
    },
    addRemark() {
      if (this.form.remark) {
        create({ comments: this.form.remark }).then(() => {
          this.getQuery()
          this.$message.success('添加成功！')
        })
      } else {
        this.$message.warning('填写意见后才能添加')
      }
    },
    setReamrk(data) {
      this.$set(this.form, 'remark', data)
      this.remarkListShow = false
    }
  }
}
