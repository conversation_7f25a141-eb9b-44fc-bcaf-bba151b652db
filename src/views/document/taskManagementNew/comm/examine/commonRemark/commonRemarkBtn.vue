<template>
  <el-dropdown class="dropdown" :disabled="remarkListShow" @command="handleCommand">
    <span class="el-dropdown-link">
      <i class="el-icon-s-order" />常用意见
    </span>
    <el-dropdown-menu slot="dropdown" class="dropdownBox">
      <el-dropdown-item v-for="(item , index) in remarkList" :key="index" class="item" icon="el-icon-circle-check" :command="item.comments">{{ item.comments }}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
export default {
  props: {
    remarkList: {
      type: Array,
      default: () => []
    },
    remarkListShow: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleCommand(item) {
      this.$emit('setReamrk', item)
    }
  }
}
</script>

<style lang='scss' scoped>
   .el-dropdown-link{
       font-size: 14px;
      .el-icon-s-order{
        color: #2a78ff;
        margin-right: 5px;
      }
   }
   .item{
      width: 400px;
      display: flex;
      align-items: baseline;
   }
   .el-dropdown [disabled] {
    cursor: pointer;
    color: #606266;
   }
   .dropdown{
     position: relative;
     .dropdownBox {
       max-height: 500px;
       overflow: auto;
       &::scrollbar {
        display: none;
      }
      &::-webkit-scrollbar {
         display: none;
      }
    }
   }
</style>
