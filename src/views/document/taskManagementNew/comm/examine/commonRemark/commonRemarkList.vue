<template>
  <ul class="remarkList">
    <li v-for="(item, index) in remarkList" :key="index" class="item" :style="editId === item.id?'background: #fff;':''">
      <div class="leftStyle">
        <el-input v-if="editId === item.id" v-model="item.comments" />
        <p v-else class="pStyle" @click="selectRemark(item)">{{ item.comments }}</p>
      </div>
      <p class="iconStyle">
        <i v-if="editId !== item.id" class="el-icon-edit remarkItemIcon edit" @click="editId = item.id" />
        <el-button v-else type="primary" size="mini" style="margin-left:8px" @click="saveComment(item)">保存</el-button>
        <el-popconfirm v-if="editId !== item.id" title="这是一段内容确定删除吗？" @confirm="batchDelComment(item.id)">
          <i slot="reference" class="el-icon-delete remarkItemIcon add" />
        </el-popconfirm>
        <el-button v-else size="mini" @click="editId = null">取消</el-button>
      </p>
    </li>
  </ul>
</template>

<script>
import { create, batchDel } from '@/api/system/user'
export default {
  props: {
    remarkList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      editId: null
    }
  },
  methods: {
    saveComment(row) {
      create(row).then(() => {
        this.$message.success('保存成功！')
        this.$emit('getQuery')
        this.editId = null
      })
    },
    batchDelComment(id) {
      batchDel([id]).then(() => {
        this.$message.success('删除成功!')
        this.$emit('getQuery')
        this.editId = null
      })
    },
    // 把常用意见赋值到文本框
    selectRemark(item) {
      this.$emit('setReamrk', item.comments)
    }
  }
}
</script>

<style lang='scss' scoped>
  .remarkList {
    overflow: auto;
    height: 100%;
     &::scrollbar {
        display: none;
      }
      &::-webkit-scrollbar {
          display: none;
      }
    .item{
      width: 100%;
      height: 28px;
      background: #fff;
      padding-left: 5px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 2px;
      .leftStyle{
        width: 100%;
      }
      .iconStyle{
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 50px;
        .add,.edit{
          font-size: 15px;
          display: inline-block;
          width: 25px;
          visibility: hidden;
          text-align: center;
        }
        .edit{
          color:#66b1ff;
        }
        .add{
          color:#f56c6c;
        }
      }
      .pStyle{
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &.item:hover .leftStyle{
        width: calc(100% - 50px);
      }
      &.item:hover .iconStyle{
        .edit{
          visibility: visible;
        }
        .add{
          visibility: visible;
        }
      }
    }
    .item:hover{
      background: #f6f6f6;
    }
  }
  ::v-deep .el-input__inner{
    height: 26px;
  }
</style>
