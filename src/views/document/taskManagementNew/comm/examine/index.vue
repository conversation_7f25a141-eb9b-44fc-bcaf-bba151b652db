<template>
  <div class="content">
    <el-form ref="form" class="forms" :model="form" status-icon>
      <!-- 办理意见、附件 -->
      <el-row :gutter="40">
        <el-col :span="24">
          <!-- 办理意见 -->
          <el-form-item v-if="!processJson.remarkDisable || processJson.remarkAttachmentAllow" :rules="fieldRules" prop="remark">
            <div class="title">
              <div class="title_left"><span v-if="!processJson.remarkBlankAllow" style="color:red;">*</span> 办理意见</div>
              <div class="title_right pointer" @click="showCommonRemark(true)">
                <commonRemarkBtn :remark-list="remarkList" :remark-list-show="remarkListShow" @setReamrk="setReamrk" />
              </div>
            </div>
            <div class="remark">
              <div class="remarkBox" :style="{ width: remarkListShow ? '50%' : '100%' }">
                <el-link class="addRemarkBtn" type="info" @click="addRemark">添加到常用意见</el-link>
                <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" show-word-limit class="textarea" resize="none" />
              </div>
              <div v-if="remarkListShow" class="frequentlyUsedRemark">
                <commonRemarkList :remark-list="remarkList" @getQuery="getQuery" @setReamrk="setReamrk" />
              </div>
            </div>
          </el-form-item>
          <!-- 附件 -->
          <el-form-item
            v-if="processJson.remarkAttachmentAllow"
            prop="fileList"
          >
            <el-upload
              ref="myUpload"
              :headers="headers"
              :action="fileUploadApi + fileName"
              :on-error="uploadError"
              :on-success="uploadSuccess"
              :file-list="form.fileList"
              :on-progress="uploadpProgress"
              :on-remove="uploadRemove"
              drag
              multiple
            >
              <div class="el-upload__text">
                <i class="el-icon-upload" />将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
        <!-- 提醒、后续办理人、预签 -->
        <el-col :span="24">
          <!-- 后续办理人 -->
          <el-form-item
            v-if="
              processJson.userTaskList && processJson.userTaskList.length !== 0
            "
          >
            <div class="title">
              <div class="title_left">后续办理人</div>
            </div>
            <div class="main">
              <step
                ref="stepUser"
                :data="processJson"
                type="processJson"
                @setFormRemark="setFormRemark"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- DING消息、短信提醒 -->
      <el-form-item v-if="false">
        <div class="itemBox">
          <div class="main">
            <el-checkbox v-model="checked1">DING消息提醒</el-checkbox>
            <el-checkbox v-model="checked2">短信提醒</el-checkbox>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <pickUser
      v-model="centerDialogVisible"
      :default-select="form.userDatas[pickUserIndex]"
      @submit="submit"
    />
  </div>
</template>

<script>
import step from './step.vue'
import pickUser from '@/components/pickUser/index.js'
import { getToken } from '@/utils/auth'

import { mapGetters } from 'vuex'

import commonRemarkBtn from './commonRemark/commonRemarkBtn.vue'
import commonRemarkList from './commonRemark/commonRemarkList.vue'
import commonRemark from './commonRemark/mixin.js'
export default {
  components: {
    step,
    commonRemarkBtn,
    commonRemarkList
  },
  mixins: [pickUser, commonRemark],
  props: {
    processJson: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      data: [],
      form: {
        remark: null,
        userTask: [],
        userDatas: [],
        flowSequence: [],
        check: null,
        checkList: [],
        fileList: []
      },
      centerDialogVisible: false,
      pickUserIndex: 0,
      fileName: '',
      headers: { Authorization: getToken() }, // 上传文件时用到的请求
      fileUploadApi: process.env.VUE_APP_BASE_API + '/api/localStorage?name=',
      remarkListShow: false,
      remarkList: [],
      editId: null,
      comments: [],
      visible: false
    }
  },
  computed: {
    ...mapGetters(['user']),
    // 处理意见规则
    fieldRules() {
      // remarkDisable 是否出现填写意见框 false:显示  true:不显示
      // remarkBlankAllow 意见是否允许为空 fasle:不允许  true :允许
      var validatePass = (rule, value, callback) => {
        if (value.match(/^[ ]*$/)) {
          callback(new Error('请输入正确的办理意见'))
        } else {
          callback()
        }
      }
      if (!this.processJson.remarkDisable) {
        if (!this.processJson.remarkBlankAllow) {
          return [
            {
              required: true,
              message: '办理意见不能为空',
              trigger: 'blur'
            },
            { validator: validatePass, trigger: 'blur' }
          ]
        } else {
          return {}
        }
      } else {
        return {}
      }
    }
  },
  created() {
    if (this.processJson.remark) {
      this.form.remark = this.processJson.remark
    }
    this.userTask()
  },
  methods: {
    getIcon(Type) {
      switch (Type) {
        case 'multi-parallel-any':
          return 'multi-parallel-any'
        case 'multi-parallel-all':
          return 'el-icon-plus'
        case 'multi-sequential':
          return 'el-icon-arrow-right'
      }
    },
    userTask() {
      if (
        this.processJson.userTaskList &&
        this.processJson.userTaskList.length !== 0
      ) {
        var userTaskList = this.processJson.userTaskList
        for (let i = 0; i < userTaskList.length; i++) {
          this.form.userTask.push(userTaskList[i].taskDefKey)
          this.form.userDatas.push(userTaskList[i].candidateUser)
          if (this.processJson.flowSequenceList !== 0) {
            this.form.flowSequence.push(
              this.flowSequence(userTaskList[i].taskDefKey)
            )
          }
        }
      }
    },
    submit(data) {
      this.$emit('data', data)
    },
    flowSequence(taskDefKey) {
      const data = this.processJson.flowSequenceList.filter((item) => {
        if (item.isDefaultFlow) {
          this.form.check = item.targetDefKey
        }
        if (item.targetDefKey === taskDefKey) {
          return item
        }
      })
      return data[0]
    },
    getPickUserIndex(data) {
      this.pickUserIndex = data
      this.centerDialogVisible = true
    },
    getUserData() {
      if (
        this.processJson.userTaskList &&
        this.processJson.userTaskList.length !== 0
      ) {
        return this.$refs.stepUser.form
      } else {
        return false
      }
    },
    setFormRemark(data) {
      if (data !== null && data !== '') {
        this.form.remark = '请' + data
      } else {
        this.form.remark = ''
      }
    },
    uploadpProgress(event, file, fileList) {
      this.fileName = file.name
    },
    uploadError() {
      this.$message.error('文件上传失败')
    },
    // 文件上传成功
    uploadSuccess(response, file, fileList) {
      this.$message.success('文件上传成功!')
      this.form.fileList = fileList
    },
    uploadRemove(file, fileList) {
      this.form.fileList = fileList
    },
    clearRemark() {
      this.form.remark = ''
    }
  }
}
</script>

<style lang='scss' scoped>
.content {
  height: 100%;
  .forms {
    height: 100%;
    ::v-deep .el-row {
      height: 100%;
      .el-col {
        height: 100%;
        .el-form-item {
          height: 100%;
          .el-form-item__content {
            height: 100%;
            .main {
              height: calc(100% - 40px);
              &::-webkit-scrollbar {
                display: none;
              }
            }
          }
        }
      }
    }
  }
  .itemBox {
    margin-bottom: 15px;
  }
  .title {
    height: 40px;
    .title_left {
      float: left;
      font-size: 16px;
      font-weight: bold;
    }
    .title_right {
      // border: 1px solid gold;
      float: right;
    }
  }

  .remark {
    height: 80px;
    // margin-bottom: 10px;
  }
  .remarkBox {
    display: inline-block;
    vertical-align: bottom;
    position: relative;
    height: 100%;
  }
  .addRemarkBtn {
    line-height: initial;
    position: absolute;
    right: 8px;
    bottom: 5px;
    z-index: 99;
  }
  .textarea {
    height: 100%;
    ::v-deep .el-textarea__inner {
      height: 100% !important;
      background: #f8f8f8;
      color: #000;
      height: 100px;
    }
  }
  .frequentlyUsedRemark {
    padding-left: 8px;
    display: inline-block;
    height: 100%;
    width: 50%;
    vertical-align: middle;
    .addBtn {
      float: right;
      padding: 4px 10px;
    }
    .remarkList {
      overflow: auto;
      height: 100%;
      &::-webkit-scrollbar {
        display: none;
      }
      .item {
        line-height: initial;
        padding: 5px;
        // cursor: pointer;
        &:hover {
          background: #eee;
        }
      }
    }
  }
}
.main {
  overflow: auto;
}
.el-icon-upload {
  font-size: 16px;
  margin: 0;
  margin-right: 5px;
}
::v-deep .el-upload {
  width: 100%;
}
::v-deep .el-upload-dragger {
  width: 100%;
  height: auto;
}
.pStyle{
  width: 100px;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
