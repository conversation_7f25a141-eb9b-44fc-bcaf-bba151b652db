<template>
  <div class="steps">
    <el-checkbox-group v-model="form.checkList">
      <div v-for="(item , index ) in data.userTaskList" :key="index" class="step">
        <template v-if="type==='processJson' && ((data.flowSequenceList && data.flowSequenceList.length>0 && data.flowSequenceList[index].flowHidden!=='true') || data.flowSequenceList.length===0)">
          <div class="step_head">
            <div v-if="index+1<data.userTaskList.length" class="step_line" />
            <el-checkbox
              v-if="form.flowSequence[index] && form.flowSequence[index].flowType && form.flowSequence[index].flowType==='multiple'"
              :label="form.flowSequence[index].targetDefKey"
              :checked="form.flowSequence[index] && form.flowSequence[index].isDefaultFlow && form.flowSequence[index].isDefaultFlow==='true'"
              @change="onChanges(form.flowSequence[index])"
            />
            <el-radio
              v-else-if="form.flowSequence[index] && form.flowSequence[index].flowType && form.flowSequence[index].flowType==='single'"
              v-model="form.check"
              :label="form.flowSequence[index].targetDefKey"
              @change="onChanges(form.flowSequence[index])"
            />
            <div v-else class="step_icon" />
          </div>
          <div class="step_main">
            <div
              class="step_main_text"
              :class="(form.flowSequence[index] && form.flowSequence[index].flowType && form.flowSequence[index].flowType==='multiple') || form.flowSequence[index] && form.flowSequence[index].flowType && form.flowSequence[index].flowType==='single'?'pointer':''"
              @click="toggleClick(form.flowSequence,index)"
            >
              <div class="step_title">{{ getTaskName(item,index) }}</div>
            </div>

            <div class="step_main_tag">
              <tags :data.sync="form.userDatas[index]" :data-index="index" :max="2" :is-del="item.userSelector && item.userSelector!=='none'" @delSelect="delSelect">
                <span v-if="item.taskExecType==='multi-parallel-any' || item.taskExecType==='fork'">/</span>
                <i v-else :class="getIcon(item.taskExecType)" />
              </tags>
            </div>
            <div v-if="item.userSelector && item.userSelector!=='none'" class="step_main_add">
              <i class="el-icon-plus add_tag" @click="DialogVisible(index,item.taskExecType,item.userSelector)" />
            </div>
          </div>
        </template>
        <template v-if="type==='preSign'">
          <div class="step_head">
            <div v-if="index+1<data.userTaskList.length" class="step_line" />
            <div class="step_icon" />

          </div>
          <div class="step_main">
            <div class="step_main_text">
              <div class="step_title">{{ item.taskName }}</div>
            </div>
            <div class="step_main_tag">
              <tags :data.sync="form.userDatas[index]" :data-index="index" :max="3" :is-del="item.userSelector && item.userSelector!=='none'" @delSelect="delSelect">
                <span v-if="item.taskExecType==='multi-parallel-any' || item.taskExecType==='fork'">/</span>
                <i v-else :class="getIcon(item.taskExecType)" />
              </tags>
            </div>
            <div v-if="item.userSelector && item.userSelector!=='none'" class="step_main_add">
              <i class="el-icon-plus add_tag" @click="DialogVisible(index,item.taskExecType,item.userSelector)" />
            </div>
          </div>
        </template>
      </div>
    </el-checkbox-group>
    <pickUser v-model="centerDialogVisible" :max="pickMax" :default-select="form.userDatas[pickUserIndex]" @submit="submit" />
    <userSelector v-if="userSelectorDialogVisible" v-model="userSelectorDialogVisible" :default-expanded="true" :user-selector="userSelector" :max="pickMax" :default-select="form.userDatas[pickUserIndex]" @submit="submit" />
  </div>
</template>
<script>
import tags from './tags'
import pickUser from '@/components/pickUser/index.js'
import userSelector from '@/components/userSelector/index.vue'
export default {
  components: { tags, userSelector },
  mixins: [pickUser],
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      form: {
        userTask: [], // 每个节点的环节名称['taske1','task2']
        userDatas: [], // 所有环节选中的人[[],[]]
        flowSequence: [], // 条件判断
        check: null, // 单选时候,当前选中的usertask
        checkList: [], // 多选时候,选中的usertask
        taskExecType: [] // '_blank 允许不选择办理人'
      },
      opinion0: '',
      opinion1: '',
      opinion2: '',
      pickUserIndex: 0,
      centerDialogVisible: false,
      test: [],
      pickMax: 0,
      userSelector: null,
      userSelectorDialogVisible: false
    }
  },
  computed: {
  },
  watch: {
    'data.userTaskList': {
      handler() {
        this.form = {
          userTask: [], // 每个节点的环节名称['taske1','task2']
          userDatas: [], // 所有环节选中的人[[],[]]
          flowSequence: [], // 条件判断
          check: null, // 单选时候,当前选中的usertask
          checkList: [], // 多选时候,选中的usertask
          taskExecType: [] // '_blank 允许不选择办理人'
        }
        this.userTask()
      },
      deep: true
    }
  },
  created() {
    this.userTask()
  },
  methods: {
    getIcon(Type) {
      switch (Type) {
        case 'multi-parallel-any':
          return 'multi-parallel-any'
        case 'multi-parallel-all':
          return 'el-icon-plus'
        case 'multi-sequential':
          return 'el-icon-arrow-right'
      }
    },
    userTask() {
      var userTaskList = this.data.userTaskList
      for (let i = 0; i < userTaskList.length; i++) {
        this.form.userTask.push(userTaskList[i].taskDefKey)
        this.form.userDatas.push(userTaskList[i].candidateUser)
        if (userTaskList[i].taskExecType && userTaskList[i].taskExecType === 'blank') {
          this.form.taskExecType.push(true)
        } else {
          this.form.taskExecType.push(false)
        }
        if (this.data.flowSequenceList && this.data.flowSequenceList !== 0) {
          this.form.flowSequence.push(this.flowSequence(userTaskList[i].taskDefKey))
        }
      }
    },
    flowSequence(taskDefKey) {
      const data = this.data.flowSequenceList.filter(item => {
        if (item.flowType === 'single' && item.isDefaultFlow && item.isDefaultFlow === 'true') {
          this.form.check = item.targetDefKey
        }
        if (item.targetDefKey === taskDefKey) {
          return item
        }
      })
      return data[0]
    },
    delSelect(Index, index) {
      this.$delete(this.form.userDatas[Index], index)
      if (this.form.flowSequence && this.form.flowSequence[Index] && this.form.flowSequence[Index].flowType && this.form.flowSequence[Index].flowType === 'multiple' && this.form.flowSequence[Index].preRule && this.form.flowSequence[Index].preRule === 'autoChecked') {
        var targetDefKey = this.form.flowSequence[Index].targetDefKey
        var targetDefKeyIndex = this.form.checkList.indexOf(targetDefKey)
        var length = this.form.userDatas[Index].length
        if (length === 0) {
          this.$delete(this.form.checkList, targetDefKeyIndex)
        }
      }
    },
    DialogVisible(index, taskExecType, userSelector) {
      if (taskExecType && taskExecType === 'single') {
        this.pickMax = 1
      } else {
        this.pickMax = 0
      }
      this.pickUserIndex = index
      if (userSelector === 'user-picker') {
        this.centerDialogVisible = true
      } else {
        this.userSelector = userSelector
        this.userSelectorDialogVisible = true
      }
    },
    submit(data) {
      this.form.userDatas[this.pickUserIndex] = data
      if (this.form.flowSequence && this.form.flowSequence[this.pickUserIndex] && this.form.flowSequence[this.pickUserIndex].flowType && this.form.flowSequence[this.pickUserIndex].flowType === 'multiple' && this.form.flowSequence[this.pickUserIndex].preRule && this.form.flowSequence[this.pickUserIndex].preRule === 'autoChecked') {
        var targetDefKey = this.form.flowSequence[this.pickUserIndex].targetDefKey
        var targetDefKeyIndex = this.form.checkList.indexOf(targetDefKey)
        if (data.length > 0 && targetDefKeyIndex === -1) {
          this.form.checkList.push(targetDefKey)
        } else if (data.length === 0) {
          this.$delete(this.form.checkList, targetDefKeyIndex)
        }
      }
    },
    onChanges(item) {
      if (item.flowType === 'single') {
        this.form.checkList = []
      } else if (item.flowType === 'multiple') {
        this.form.check = null
      }
    },
    toggleClick(flowSequence, index) {
      if (flowSequence && flowSequence[index]) {
        if (flowSequence[index].flowType === 'single') {
          this.form.checkList = []
          this.$set(this.form, 'check', flowSequence[index].targetDefKey)
        } else if (flowSequence[index].flowType === 'multiple') {
          var isfind = this.form.checkList.findIndex(item => {
            return flowSequence[index].targetDefKey === item
          })
          if (isfind === -1) {
            this.form.checkList.push(flowSequence[index].targetDefKey)
          } else {
            this.$delete(this.form.checkList, isfind)
          }
          this.form.check = null
        }
      }
    },
    getTaskName(item, index) {
      if (this.form.flowSequence && this.form.flowSequence[index] && this.form.flowSequence[index].sequenceName) {
        return this.form.flowSequence[index].sequenceName
      } else {
        return item.taskName
      }
    }
  }
}
</script>

<style lang='scss' scoped>
     .step{
      display: flex;
      position: relative;
      flex-shrink: 1;
      align-items: center;
      &:last-child .step_line{
        display: none;
      }
      .step_head{
        flex-grow: 0;
        width: 24px;
        height: 27px;
        position: relative;
        .step_line{
          width: 2px;
          bottom: 0;
          left: 5px;
          margin-right: 0px;
          position: absolute;
          left: 6px;
          top: 21px;
          border-color: inherit;
          background-color: #1890ff;
          height: 100%;
        }
        .step_icon{
          border-radius: 50%;
          border: 7px solid #04386B;
          background: #04386B;
          border-color: inherit;
          position: relative;
          z-index: 1;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          box-sizing: border-box;
          transition: .15s ease-out;
        }
        ::v-deep .el-radio__label,::v-deep .el-checkbox__label{
          display: none;
        }
      }
      .step_main{
        padding: 2px 0;
        padding-left: 10px;
        flex-grow: 1;
        white-space: normal;
        text-align: left;
        border-bottom: 1px dashed #eee;
        position: relative;
        display: flex;
        align-items: center;
        .step_main_text{
          flex: 4;
          .step_title{
            line-height: 24px;
            width: 150px;
            // padding-bottom: 8px;
            font-size: 16px;
          }
          .step_description{
            padding-right: 10%;
            margin-top: -5px;
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
            color: #B5B5B5;
          }
        }
        .step_main_tag{
          flex: 15;
          width: 140px;
          height: 38px;
          // background-color: #ddd;
          overflow: hidden;
          .el-icon-arrow-right{
              margin:0 3px;
          }
        }
        .step_main_add{
          margin-left: 8px;
          .add_tag{
            cursor: pointer;
            color: #1890ff;
            display: inline-block;
            height: 28px;
            width: 28px;
            line-height: 28px;
            border-radius: 4px;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            white-space: nowrap;
            border:1px dashed #1890ff;
            text-align: center;
            font-size: 14px;
            vertical-align: middle;
            &:hover{
              color: #000;
              border-color:#0971d3;
            }
          }
        }
        // .divStyle{
        //   width: 200px;
        //   height: 50px;
        //   background-color: #000;
        // }
        .step_main_text,.step_main_tag,.step_main_add{
          vertical-align: middle;
          // height: 40px;
          // line-height: 40px;
        }
      }
    }
</style>
