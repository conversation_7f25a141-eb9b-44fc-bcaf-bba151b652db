<template>
  <div class="tagBox">
    <ul class="tags">
      <li v-if="data.length>max">
        <el-tag class="pointer tagItem" effect="dark" @click="dialogVisible=true,gengduo()"> 更多</el-tag>
        <span class="tag_line">
          <slot />
        </span>
      </li>
      <li v-for="(tag , index) in getData" :key="index">
        <el-tag :closable="isDel" type="info" class="tagItem" @close="delSelect(index)">
          {{ tag.nickName }}
        </el-tag>
        <span v-if="index !== getData.length-1" class="tag_line">
          <slot />
        </span>
      </li>
    </ul>
    <el-dialog title="更多" :close-on-click-modal="false" :visible.sync="dialogVisible" width="800px" append-to-body>
      <ul>
        <li v-for="(tag , index) in data" :key="index">
          <el-tag :closable="isDel" type="info" style="margin-top:5px" @close="delSelect(index)">
            {{ tag.nickName }}
          </el-tag>
          <span v-if="index !== data.length-1" class="tag_line">
            <slot />
          </span>
        </li>
      </ul>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      required: true
    },
    max: {
      type: Number,
      default: 0
    },
    dataIndex: {
      type: Number,
      default: 0
    },
    dialogTitle: {
      type: String,
      default: '更多'
    },
    isDel: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      dialogVisible: false
    }
  },
  computed: {
    getData() {
      if (this.max && this.max > 0) {
        return this.data.slice(0, this.max)
      } else {
        return this.data
      }
    }
  },

  methods: {
    delSelect(index) {
      this.$emit('delSelect', this.dataIndex, index)
    }
  }
}
</script>

<style lang='scss' scoped>
    .tags{
        text-align: right;
    }
    .tagItem{
      vertical-align: middle;
    }
    li{
    display: inline-block;
    }
    .tag_line{
        font-size: 12px;
        padding: 0 8px;
        position: relative;
        top: 2px;
    }
    ::v-deep .el-dialog__header{
        line-height: initial;
    }
    ::v-deep .el-dialog__body{
        height: 400px;
        overflow: auto;
    }
</style>
