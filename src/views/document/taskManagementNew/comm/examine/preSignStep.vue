<template>
  <div>
    <div v-for="(item , index) in data" ref="stepUser" :key="index" class="item">
      <step :ref="item.taskDefKey" :data="item" class="itemStep" type="preSign" />
    </div>
  </div>
</template>

<script>
import step from './step.vue'
export default {
  components: { step },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: []
    }
  },
  methods: {
    getstepUser() {
      var arr = []
      for (let i = 0; i < this.data.length; i++) {
        var child = this.data[i]['taskDefKey']
        arr.push(this.$refs[child][0].form)
      }
      return arr
    }
  }
}
</script>

<style lang='scss' scoped>
    // .item{
    //     margin:0 10px 15px;
    //     .itemName{
    //         font-weight: bold;
    //     }
    //     .itemStep{
    //         margin: 0 10px;
    //     }
    // }
</style>
