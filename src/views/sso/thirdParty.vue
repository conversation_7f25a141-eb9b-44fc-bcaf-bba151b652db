<template>
  <div class="sso">
    <template v-if="isOpen">
      <el-empty :image-size="300" :image="require('@/assets/images/empty/approval.png')" description=" ">
        <el-button type="primary" @click="openWindow">新窗口中打开</el-button>
      </el-empty>
    </template>
    <template v-else>
      <div v-if="showBack" class="back" tltle="显示菜单" @click="back">
        <i class="el-icon-arrow-left" />
      </div>
      <iframe v-if="serviceName" :src="url" frameborder="0" style="width:100%; height:100%;" />
      <el-result v-else icon="error" title="非法请求" sub-title="serviceName参数不能为空！" />
    </template>
  </div>
</template>

<script>
import ssoApi from '@/api/sso/index'

export default {
  data() {
    return {
      url: 'about:blank'
    }
  },
  computed: {
    serviceName: function() {
      return this.$route.query.serviceName
    },
    showBack() {
      var sidebarHide = this.$route.query.sidebarHide
      var navbar = this.$route.query.navbar
      return sidebarHide === 'false' && navbar === 'false'
    },
    isOpen() {
      var isOpen = this.$route.query.openWindow
      if (isOpen && isOpen === 'true') {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.getSsoRedirect()
  },
  methods: {
    getSsoRedirect() {
      ssoApi.thirdParty({ serviceName: this.serviceName }).then(res => {
        this.url = res.redirect
        if (this.isOpen) {
          this.openWindow()
        }
      })
    },
    back() {
      this.$router.back()
    },
    openWindow() {
      window.open(this.url)
    }
  }
}
</script>

<style  lang="scss" scoped>
 .sso{
   width: 100%;
   height: 100%;
   position: relative;
   .back{
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    color: #409eff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 6px ragb(0 0 0 / 12%);
    cursor: pointer;
    font-size: 20px;
    position: absolute;
    left: 10px;
    top: 10px;
   }
 }
</style>
